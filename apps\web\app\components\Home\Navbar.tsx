// app/components/layout/Navbar.tsx
import { Link, useLocation, useNavigate } from "@remix-run/react";
import { useState } from "react";
import axios from "axios";

interface NavLinkProps {
  to: string;
  children: React.ReactNode;
  isActive: boolean;
}

const NavLink = ({ to, children, isActive }: NavLinkProps) => (
  <Link 
    to={to} 
    className={`font-outfit transition-colors px-4 ${isActive ? 'text-orospaces-purple' : 'text-gray-800 hover:text-gray-600'}`}
  >
    {children}
  </Link>
);

const Navbar = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  
  const isActivePath = (path: string) => {
    if (path === "/" && location.pathname === "/") return true;
    if (path !== "/" && location.pathname.startsWith(path)) return true;
    return false;
  };
  

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };
  

  return (
    <nav className="w-full py-4 px-4 md:px-7 border-b border-gray-200 bg-white">
      <div className="mx-auto flex items-center justify-between">
        <Link to="/" className="flex-shrink-0">
          <h1 className="text-2xl font-space-grotesk font-bold text-indigo-900">Orospaces</h1>
        </Link>
        
        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center justify-center flex-1 space-x-2">
          <NavLink to="/" isActive={isActivePath("/")}>Home</NavLink>
          <NavLink to="/for-owners" isActive={isActivePath("/for-owners")}>For Owners</NavLink>
          <NavLink to="/for-builders" isActive={isActivePath("/for-builders")}>For Builders</NavLink>
          <NavLink to="/blogs" isActive={isActivePath("/blogs")}>Blogs</NavLink>
          <NavLink to="/about" isActive={isActivePath("/about")}>About Us</NavLink>
        </div>
        
        <div className="flex items-center space-x-4">
          <Link 
            to="/projects/post" 
            className="hidden md:block bg-orospaces-purple hover:bg-indigo-600 text-white py-2 px-4 rounded-md font-outfit transition-colors"
          >
            Post Project
          </Link>
          
          <button
            onClick={() => navigate('/login')}
            className="hidden md:block border border-orospaces-purple text-orospaces-purple hover:text-indigo-700 hover:border-indigo-700 py-2 px-4 rounded-md font-outfit transition-colors"
          >
            Log In
          </button>
          
          {/* Mobile menu button */}
          <button 
            className="md:hidden text-orospaces-purple"
            onClick={toggleMobileMenu}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-col space-y-3 px-2">
            <NavLink to="/" isActive={isActivePath("/")}>Home</NavLink>
            <NavLink to="/for-owners" isActive={isActivePath("/for-owners")}>For Owners</NavLink>
            <NavLink to="/for-builders" isActive={isActivePath("/for-builders")}>For Builders</NavLink>
            <NavLink to="/blogs" isActive={isActivePath("/blogs")}>Blogs</NavLink>
            <NavLink to="/about" isActive={isActivePath("/about")}>About Us</NavLink>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;