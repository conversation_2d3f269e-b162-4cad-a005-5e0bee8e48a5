import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";
import type { User } from "../services/types";


type UserContextType = {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  setUser: (user: User | null) => void;
  checkAuth: () => Promise<boolean>;
};

const UserContext = createContext<UserContextType | undefined>(undefined);

// Client-side API function to fetch current user
// This relies on the browser sending the correct cookie if your backend API uses cookie auth for /user/me.
// If /user/me strictly expects a Bearer token, this client-side fetch will fail unless
// you implement a Remix resource route that proxies this call using the API token from the session.
async function fetchCurrentUserOnClient(): Promise<User | null> {
  try {
    // This call to localhost:4000/user/me directly from the client
    // will only work with cookie-based auth if your API and frontend are on compatible domains/paths
    // and your API's `authorize` function handles the cookie.
    // It will NOT use the Authorization: Bearer token from the Remix session.
    const response = await axios.get<{ user: User }>(`http://localhost:4000/user/me`, {
      withCredentials: true // For sending cookies to the API
    });
    return response.data.user || null; // Assuming backend returns { user: UserData }
  } catch (error) {
    // console.error("Client-side error fetching user:", error);
    // It's common for this to fail if not authenticated, so avoid flooding console
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      // Unauthorized, expected if no valid session cookie for the API
    } else {
      console.error("Client-side error fetching user:", error);
    }
    return null;
  }
}

export function UserProvider({ 
  children, 
  initialUser 
}: { 
  children: React.ReactNode; 
  initialUser?: User | null; // Make initialUser optional
}) {
  const [user, setUserState] = useState<User | null>(initialUser || null);
  // isLoading should reflect initial server load status.
  // If initialUser is provided, we are not initially loading on client.
  const [isLoading, setIsLoading] = useState(!initialUser && initialUser !== null);
  
  // This effect ensures that if initialUser changes (e.g. after login/logout and root loader re-runs),
  // the context state is updated.
  useEffect(() => {
    if (initialUser !== undefined) { // Check if initialUser prop was actually passed
        setUserState(initialUser);
        setIsLoading(false); // Data is from server, so not loading on client initially
    }
  }, [initialUser]);


  const setUser = (newUser: User | null) => {
    setUserState(newUser);
  };

  const checkAuth = async () => {
    setIsLoading(true);
    try {
      const userData = await fetchCurrentUserOnClient(); // Use the client-side fetch
      setUserState(userData);
      setIsLoading(false);
      return !!userData;
    } catch (error) {
      console.error("Auth check error (client-side):", error);
      setUserState(null);
      setIsLoading(false);
      return false;
    }
  };
  
  // If initialUser is explicitly null (meaning loader ran and found no user),
  // we can consider isLoading false from the start.
  useEffect(() => {
    if (initialUser === null) {
      setIsLoading(false);
    }
  }, [initialUser]);


  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    setUser,
    checkAuth,
  };
  
  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}

// The server-side requireAuth utility should live in a .server.ts file,
// like the one you have in app/utils/auth.server.ts.
// Remove it from this client-side context file.
// export async function requireAuth(request: Request) { ... }