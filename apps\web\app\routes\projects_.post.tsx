import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useRef } from "react";
import {
  MultiStepForm,
  MultiStepFormStep,
  MultiStepFormHeader,
  MultiStepFormFooter,
  createStepSchema,
  MultiStepFormContextProvider
} from "../components/MultiStepForm";
import FormNavigation from "../components/ClientFormNavigation";
import { ClientOnly } from "remix-utils/client-only";
import { cn } from "../lib/utils";

const GOOGLE_MAPS_API_KEY = "AIzaSyCgZjXgPNG0SDagPLz7MMfoWFrueJ5d6Sk";

const formSchema = createStepSchema({
  basicInfo: z.object({
    state: z.string().min(1, "State cannot be empty"),
    city: z.string().min(1, "City cannot be empty"),
    pincode: z.string().min(3, "<PERSON>nco<PERSON> cannot be empty"),
    address: z.string().min(1, "Address cannot be empty"),
    landIdentifier: z.string().optional(),
  }),
  details: z.object({
    landType: z.string().min(1, "Please select a land type"),
    projectType: z.string().min(1, "Please select a project type"),
    area: z.string().min(1, "Land area is required"),
    price: z.string().optional(),
    title: z.string().optional(),
    description: z.string().optional()
  }),
  photos: z.object({
    images: z.array(z.instanceof(File))
      .min(1, "Please upload at least 1 photo")
      .max(10, "Maximum 10 photos allowed")
      .refine(
        (files) => files.every(file =>
          ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'].includes(file.type)
        ),
        "Only PNG, JPG, JPEG, and GIF formats are supported"
      )
      .refine(
        (files) => files.every(file => file.size <= 10 * 1024 * 1024),
        "Each image must be less than 10MB"
      ),
    thumbnail: z.number().int().min(0).optional()
  }),
  documents: z.object({
    documents: z.array(z.instanceof(File))
      .min(0, "Documents are optional")
      .max(5, "Maximum 5 documents allowed")
      .refine(
        (files) => files.every(file =>
          ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
        ),
        "Only PDF and DOC/DOCX formats are supported"
      )
      .refine(
        (files) => files.every(file => file.size <= 15 * 1024 * 1024),
        "Each document must be less than 15MB"
      ),
  }),
  review: z.object({})
});

type formValues = z.infer<typeof formSchema>;

interface FormProgressProps {
  steps: string[];
  currentStepIndex: number;
}

function FormProgress({ steps, currentStepIndex }: FormProgressProps) {
    // Only show the first 4 steps in the progress bar (excluding review)
    const visibleSteps = steps.slice(0, 4);
   
    return (
      <div className="lg:absolute lg:top-[-100px] lg:z-50 lg:h-full mb-8 lg:mb-0 rounded-sm p-1 lg:p-7 lg:border lg:border-neutral-300 overflow-hidden bg-white">
        <h1 className="pb-4 lg:pb-10 pl-1 font-semibold font-inter text-2xl lg:text-wrap">Create your ad with <br></br> Orospaces</h1>
        <div className="flex flex-col lg:flex-row gap-y-2 lg:gap-x-4 w-full overflow-x-clip">
          <div className="relative lg:h-[300px] h-auto w-min lg:w-min overflow-x-auto">
            <div className="relative lg:min-w-full lg:h-full py-2 pl-1 pr-2 min-w-min overflow-x-auto lg:overflow-visible scrollbar-hide w-[85vw] lg:w-auto place-self-center">
              <div className="min-w-0 relative h-full">
                <div className="relative h-full w-full">
                 
                  <div
                    className="absolute h-1 bg-gray-200 top-1/2 -translate-y-1/2 lg:hidden left-[20px] right-[20px]"
                  />
                 
                  <div
                    className="absolute w-1 h-[90%] bg-gray-200 left-1/2 -translate-x-1/2 hidden lg:block"
                  />
 
                  <div
                    className="absolute h-1 bg-orospaces-purple transition-all duration-300 top-1/2 -translate-y-1/2 lg:hidden left-[20px]"
                    style={{
                      width: `calc((100% - 45px) * ${Math.min(currentStepIndex, visibleSteps.length - 1) / (visibleSteps.length - 1)})`,
                    }}
                  />
 
                  <div
                    className="absolute w-1 bg-orospaces-purple transition-all duration-300 left-1/2 -translate-x-1/2 hidden lg:block"
                    style={{
                      height: `calc(${(Math.min(currentStepIndex, visibleSteps.length - 1) / (visibleSteps.length - 1)) * 100}% - ${currentStepIndex === 0 ? 18 : currentStepIndex >= visibleSteps.length - 1 ? 18 : 0}px)`,
                    }}
                  />
 
                 
                  <div className="relative flex lg:flex-col flex-row justify-between h-full w-full">
                    {visibleSteps.map((step, index) => {
                      const isActive = index <= Math.min(currentStepIndex, visibleSteps.length - 1);
                      const isCurrent = index === Math.min(currentStepIndex, visibleSteps.length - 1);
 
                      return (
                        <div
                          key={index}
                          className="flex items-center justify-center"
                        >
                          <div
                            className={cn(
                              "h-7 w-7 rounded-full border-2 transition-all duration-300",
                              isActive
                                ? "border-orospaces-purple bg-orospaces-purple"
                                : "border-gray-300 bg-white",
                              isCurrent && "ring-4 ring-blue-100"
                            )}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
 
         
          <div className="hidden lg:block lg:w-auto lg:overflow-visible scrollbar-hide max-w-full">
          <div className="min-w-fit lg:min-w-0 flex lg:flex-col flex-row items-start justify-between lg:gap-y-12">
              {visibleSteps.map((step, index) => {
                const isActive = index <= Math.min(currentStepIndex, visibleSteps.length - 1);
                return (
                  <div
                    key={step}
                    className="flex lg:flex-row flex-col px-2 lg:px-0"
                  >
                    <span
                      className={cn(
                        "text-xs lg:text-sm font-medium font-inter lg:whitespace-nowrap truncate lg:text-nowrap",
                        isActive ? "text-orospaces-purple" : "text-gray-500",
                      )}
                    >
                      {step} <br className="hidden lg:block font-inter"></br> Step - {index + 1}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Mobile step names */}
          <div className="flex lg:hidden justify-between w-full space-x-14 mt-2 overflow-x-scroll scrollbar-hide">
            {visibleSteps.map((step, index) => {
              const isActive = index <= Math.min(currentStepIndex, visibleSteps.length - 1);
              return (
                <span
                  key={step}
                  className={cn(
                    "text-xs font-medium font-inter truncate text-wrap",
                    isActive ? "text-orospaces-purple" : "text-gray-500"
                  )}
                >
                  {step}
                </span>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

const steps = ["Basic Details", "Project Details", "Project Photos", "Documents & Others"];

export default function homePage() {
  const form = useForm<formValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      basicInfo: { state: "", city: "", pincode: "", address: "", landIdentifier: "" },
      details: {
        landType: "",
        projectType: "",
        area: "",
        price: "",
        title: "",
        description: ""
      },
      photos: {
        images: [],
        thumbnail: 0
      },
      documents: {
        documents: []
      },
    }
  });

  const autocompleteContainerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const initMap = async (): Promise<void> => {
      try {
        // Request needed libraries
        await google.maps.importLibrary("places") as google.maps.PlacesLibrary;
        
        // Create the Google Maps Places Autocomplete element
        // @ts-ignore
        const placeAutocomplete = new google.maps.places.PlaceAutocompleteElement();
        
        // Clear any previous instances
        if (autocompleteContainerRef.current) {
          autocompleteContainerRef.current.innerHTML = '';
          // Append the autocomplete element to our container
          autocompleteContainerRef.current.appendChild(placeAutocomplete);
        }
        
        // Add the gmp-placeselect listener
        // @ts-ignore
        placeAutocomplete.addEventListener('gmp-select', async ({ placePrediction }) => {
          const place = placePrediction.toPlace();
          await place.fetchFields({ 
            fields: ['displayName', 'formattedAddress', 'location', 'addressComponents'] 
          });
          
          // Update form values with the selected place data
          if (place.formattedAddress) {
            form.setValue("basicInfo.address", place.formattedAddress);
          }
          
          // Extract address components if available
          if (place.addressComponents) {
            const components = place.addressComponents;
            
            // Find postal code
            const postalCode = components.find(component => 
              component.types.includes("postal_code")
            );
            if (postalCode) {
              form.setValue("basicInfo.pincode", postalCode.longText);
            }
            
            // Find city
            const city = components.find(component => 
              component.types.includes("locality") || 
              component.types.includes("sublocality") ||
              component.types.includes("administrative_area_level_2")
            );
            if (city) {
              form.setValue("basicInfo.city", city.longText);
            }
            
            // Find state
            const state = components.find(component => 
              component.types.includes("administrative_area_level_1")
            );
            if (state) {
              form.setValue("basicInfo.state", state.longText);
            }
          }
        });
      } catch (error) {
        console.error("Error initializing Google Maps Places Autocomplete:", error);
      }
    };
    
    // Initialize the map when the component mounts
    if (typeof window !== "undefined") {
      if (window.google?.maps) {
        initMap();
      } else {
        // Add script to load Google Maps API if not already loaded
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places&callback=initMapCallback`;
        script.async = true;
        script.defer = true;
        
        // Define the callback function
        window.initMapCallback = () => {
          initMap();
        };
        
        document.head.appendChild(script);
      }
    }
    
    return () => {
      // Cleanup if necessary
    };
  }, [form]);

  const onSubmit = async (data: formValues) => {
    console.log("", data);
  };

  const onSaveAsDraft = async () => {
    const data = form.getValues();
    console.log("", data);
  };

  return (
    <>
    <img className="w-full h-full object-contain" src="/Banner.svg" />
    <div className="p-5 lg:p-10">
      <ClientOnly fallback={<div>Loading form...</div>}>
        {() => (
          <div className="">
          <MultiStepForm
            schema={formSchema}
            form={form}
            onSubmit={onSubmit}
            className="relative "
          >
            <MultiStepFormHeader>
            <MultiStepFormContextProvider>
              {(context) => (
                <FormProgress
                  steps={steps}
                  currentStepIndex={context.currentStepIndex}
                />
              )}
            </MultiStepFormContextProvider>
            </MultiStepFormHeader>          
           
            {/* Step 1: Basic Information */}
            <MultiStepFormStep name="basicInfo">
            <h1 className="text-xl font-semibold font-inter py-4">Add Basic Details</h1>
            <h3 className="text-[#7A7A7A] font-inter pb-8">Adding accurate location helps you connect with the right buyers</h3>
              <div className="space-y-8">
                {/* Google Maps Places Autocomplete */}
                <div className="mb-4">
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full mb-2">
                    Search for Location
                  </label>
                  <div ref={autocompleteContainerRef} className="w-full"></div>
                </div>
              
                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full">
                    State
                  </label>
                  <input
                    {...form.register("basicInfo.state")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter State"
                  />
                  {form.formState.errors.basicInfo?.state && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.basicInfo.state.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full">
                    City
                  </label>
                  <input
                    {...form.register("basicInfo.city")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter City"
                  />
                  {form.formState.errors.basicInfo?.city && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.basicInfo.city.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full">
                    Pincode
                  </label>
                  <input
                    {...form.register("basicInfo.pincode")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter Pincode"
                  />
                  {form.formState.errors.basicInfo?.pincode && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.basicInfo.pincode.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full">
                    Address
                  </label>
                  <input
                    {...form.register("basicInfo.address")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter Complete Address"
                  />
                  {form.formState.errors.basicInfo?.address && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.basicInfo.address.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] w-full">
                    Land/Building/Society/Project (Optional)
                  </label>
                  <input
                    {...form.register("basicInfo.landIdentifier")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter Project Name"
                  />
                  {form.formState.errors.basicInfo?.landIdentifier && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.basicInfo.landIdentifier.message}
                    </p>
                  )}
                </div>
              </div>
            </MultiStepFormStep>

            {/* Step 2: Project Details */}
            <MultiStepFormStep className="font-inter" name="details">
              <h1 className="text-xl font-semibold font-inter py-4">Add Project Details</h1>
              <h3 className="text-[#7A7A7A] font-inter pb-8">Add Project Details to set the project profile</h3>
            
              <div className="space-y-6">

              <div>
                  <label className="block text-sm font-medium text-[#7A7A7A] mb-3">
                    Land Type
                  </label>
                  <div className="grid grid-cols-3 gap-4">
                    {[
                      { id: "Agricultural", label: "Agricultural" },
                      { id: "Open Land", label: "Open Land" },
                      { id: "Non Agricultural", label: "Non-Agricultural" }
                    ].map((type) => (
                      <div
                        key={type.id}
                        className={`
                            text-center cursor-pointer transition-all
                          ${form.watch("details.landType") === type.id
                            ? "border-gray-300 p-4 border-2 rounded-lg"
                            : ""
                          }
                        `}
                        onClick={() => form.setValue("details.landType", type.id)}
                      >
                        <div className="bg-gray-100 hover:bg-gray-200 w-full h-24 mb-2 rounded-md"></div>
                        <span className="block text-sm font-medium">{type.label}</span>
                      </div>
                    ))}
                  </div>
                  {form.formState.errors.details?.landType && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.landType.message}
                    </p>
                  )}
                </div>
              
                <div>
                  <label className="block text-sm font-medium font-inter text-[#7A7A7A] mb-3">
                    Project Type
                  </label>
                  <div className="grid grid-cols-3 gap-4">
                    {[
                      { id: "Redevelopment", label: "Redevelopment" },
                      { id: "SRA", label: "SRA Project" },
                      { id: "Commercial", label: "Commercial" }
                    ].map((type) => (
                      <div
                        key={type.id}
                        className={`
                            text-center cursor-pointer transition-all
                          ${form.watch("details.projectType") === type.id
                            ? "border-gray-300 p-4 border-2 rounded-lg"
                            : ""
                          }
                        `}
                        onClick={() => form.setValue("details.projectType", type.id)}
                      >
                        <div className="bg-gray-100 hover:bg-gray-200 w-full h-24 mb-2 rounded-md"></div>
                        <span className="block text-sm font-medium">{type.label}</span>
                      </div>
                    ))}
                  </div>
                  {form.formState.errors.details?.projectType && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.projectType.message}
                    </p>
                  )}
                </div>

              
                <div>
                  <label className="block text-sm font-medium text-[#7A7A7A] w-full">
                    Land Area
                  </label>
                  <input
                    type="text"
                    {...form.register("details.area")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter Land Size (sq.ft.)"
                  />
                  {form.formState.errors.details?.area && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.area.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-[#7A7A7A] w-full">
                    Budget Estimate (Optional)
                  </label>
                  <input
                    type="text"
                    {...form.register("details.price")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Enter Amount in INR"
                  />
                  {form.formState.errors.details?.price && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.price.message}
                    </p>
                  )}
                </div>
              
                <div className="py-5">
                <h1 className="text-lg font-semibold mb-2">Add Project Description</h1>
                <h3 className="text-sm font-neutral-300">Adding Title and Description increases your chances of getting noticed.</h3>
                </div>

                <div>
                  <label className="block text-sm font-medium text-[#7A7A7A] w-full">
                    Project Title (Optional)
                  </label>
                  <input
                    type="text"
                    {...form.register("details.title")}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Add a catchy project title"
                  />
                  {form.formState.errors.details?.title && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.title.message}
                    </p>
                  )}
                  <h3 className="text-xs text-neutral-500 mt-2">Get High Performing Title Recommendations with <span className="text-orospaces-purple cursor-pointer font-semibold underline hover:text-orospaces-purple/80">Pro Plan</span></h3>
                </div>


                <div>
                  <label className="block text-sm font-medium text-[#7A7A7A] w-full">
                    Project Description (Optional)
                  </label>
                  <textarea
                    {...form.register("details.description")}
                    rows={4}
                    className="mt-2 p-2 block w-full rounded-md border-2 border-neutral-200"
                    placeholder="Describe your project in 250 characters..."
                  />
                  {form.formState.errors.details?.description && (
                    <p className="mt-1 text-sm text-red-600">
                      {form.formState.errors.details.description.message}
                    </p>
                  )}
                  <h3 className="text-xs text-neutral-500 mt-2">Get Automatic Description for your Project with <span className="text-orospaces-purple cursor-pointer font-semibold underline hover:text-orospaces-purple/80">Pro Plan</span></h3>
                </div>
              </div>
            </MultiStepFormStep>

            {/* Step 3: Photos */}
            <MultiStepFormStep className="font-inter" name="photos">
              <div className="space-y-6">
              
                <div>
                  <h1 className="text-2xl font-semibold text-gray-800">
                    Add Photos of your Project
                  </h1>

                  <h5 className="mt-2 text-gray-600">
                      A picture is worth a thousand words. 90% of viewers look at photos before acting on the ad.
                    </h5>
                
                  {form.formState.errors.photos?.images && (
                    <h5 className="mt-2 text-red-600 font-medium">
                      {form.formState.errors.photos?.images.message}
                    </h5>
                  )}
                </div>
              
                <h2 className="text-xl font-medium text-neutral-500">Add upto 10 Photos (Optional)</h2>
              
              
                <div className={cn(
                  (form.watch("photos.images")?.length || 0) === 0
                    ? "w-full" // Full width container when no images
                    : "lg:w-[37vw] xl:w-[47vw] grid grid-cols-2 gap-4" // Grid layout when images exist
                )}>
                
                  {form.watch("photos.images")?.map((file, index) => {
                    const isThumbnail = form.watch("photos.thumbnail") === index;
                    return (
                      <div key={index} className="relative rounded-lg overflow-hidden h-48 bg-gray-100">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Upload ${index + 1}`}
                          className="w-full h-full object-contain"
                        />
                      
                      
                        {isThumbnail ? (
                          <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-3 py-1 rounded-md text-sm">
                            Thumbnail
                          </div>
                        ) : (
                          <button
                            type="button"
                            className="absolute top-2 left-2 bg-gray-200 bg-opacity-80 text-gray-800 px-1 py-1 rounded-md text-sm flex items-center gap-1 hover:bg-opacity-100"
                            onClick={() => {
                              form.setValue("photos.thumbnail", index);
                            }}
                          >
                            <span className="inline-block w-4 h-4 rounded-full border font-inter border-gray-500"></span>
                            Thumbnail
                          </button>
                        )}
                      
                      
                        <button
                          type="button"
                          className="absolute top-2 right-2 bg-black bg-opacity-60 text-white rounded-full p-1 hover:bg-opacity-80"
                          onClick={() => {
                            const files = form.getValues("photos.images");
                            const newFiles = files.filter((_, i) => i !== index);
                            form.setValue("photos.images", newFiles);
                          
                            // Update thumbnail index if needed
                            const thumbnailIndex = form.getValues("photos.thumbnail");
                            if (thumbnailIndex === index) {
                              form.setValue("photos.thumbnail", newFiles.length > 0 ? 0 : 0);
                            } else if (typeof thumbnailIndex === 'number' && thumbnailIndex > index) {
                              form.setValue("photos.thumbnail", thumbnailIndex - 1);
                            }
                          
                            form.trigger("photos.images");
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    );
                  })}
                
                
                  {(form.watch("photos.images")?.length || 0) < 10 && (
                    <div className={cn(
                      "border-2 border-dashed border-purple-200 rounded-lg bg-purple-50 flex flex-col items-center justify-center text-center",
                      // Conditional styling based on whether images exist
                      (form.watch("photos.images")?.length || 0) === 0
                        ? "p-10" // Larger padding when standalone
                        : "h-48 p-4" // Fixed height when in grid
                    )}>
                      <div className={cn(
                        "bg-purple-500 rounded-full text-white",
                        (form.watch("photos.images")?.length || 0) === 0
                          ? "p-4 mb-4" // Larger icon when standalone
                          : "hidden" // Smaller icon when in grid
                      )}>
                        <svg xmlns="http://www.w3.org/2000/svg"
                          className={cn(
                            (form.watch("photos.images")?.length || 0) === 0
                              ? "h-8 w-8" // Larger icon when standalone
                              : "hidden" // Smaller icon when in grid
                          )}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    
                      <p className={cn(
                        "text-purple-600 font-medium",
                        (form.watch("photos.images")?.length || 0) === 0
                          ? "text-base mb-2" // Larger text when standalone
                          : "text-sm mb-1" // Smaller text when in grid
                      )}>
                        {(form.watch("photos.images")?.length || 0) === 0
                          ? "Add atleast 5 photos for better results"
                          : "Drag and drop your photos here"}
                      </p>
                    
                      {(form.watch("photos.images")?.length || 0) === 0 && (
                        <p className="text-gray-500 mb-2">
                          Drag and drop your photos here
                        </p>
                      )}
                    
                      <p className={cn(
                        "text-gray-400",
                        (form.watch("photos.images")?.length || 0) === 0
                          ? "text-sm mb-4" // More space when standalone
                          : "text-xs mb-3" // Less space when in grid
                      )}>
                        Formats: png, jpg, jpeg, gif | Max size: 10 MB
                      </p>
                    
                      <label htmlFor="file-upload" className={cn(
                        "inline-block border border-gray-300 rounded-md text-gray-700 bg-white cursor-pointer hover:bg-gray-50",
                        (form.watch("photos.images")?.length || 0) === 0
                          ? "px-6 py-2 text-base" // Larger button when standalone
                          : "px-2 py-1 text-sm" // Smaller button when in grid
                      )}>
                        Upload Photos
                        <input
                          id="file-upload"
                          type="file"
                          multiple
                          accept=".png,.jpg,.jpeg,.gif"
                          className="sr-only"
                          onChange={(e) => {
                            if (e.target.files) {
                              const files = Array.from(e.target.files);
                              const currentFiles = form.getValues("photos.images") || [];
                            
                            
                              const validFiles = files.filter(file =>
                                ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'].includes(file.type)
                              );
                            
                            
                              const sizeValidFiles = validFiles.filter(file =>
                                file.size <= 10 * 1024 * 1024
                              );
                            
                            
                              const newFiles = [...currentFiles, ...sizeValidFiles].slice(0, 10);
                            
                              form.setValue("photos.images", newFiles);
                            
                            
                              if (form.getValues("photos.thumbnail") === undefined && newFiles.length > 0) {
                                form.setValue("photos.thumbnail", 0);
                              }
                            
                              form.trigger("photos.images");
                              e.target.value = ''; // Reset input
                            }
                          }}
                        />
                      </label>
                    </div>
                  )}
                </div>
              
              
                <p className="text-sm text-gray-500">
                  {(form.watch("photos.images")?.length || 0)} of 10 photos uploaded
                </p>
              </div>
            </MultiStepFormStep>

            {/* Step 4: Documents */}
            <MultiStepFormStep name="documents">
              <div className="space-y-6">
              
                <div>
                  <h1 className="text-2xl font-semibold text-gray-800">
                    Add Documents for your Project
                  </h1>

                  <h5 className="mt-2 text-gray-600">
                    Upload relevant documents like approvals, certificates, or plans.
                  </h5>
                
                  {form.formState.errors.documents?.documents && (
                    <h5 className="mt-2 text-red-600 font-medium">
                      {form.formState.errors.documents?.documents.message}
                    </h5>
                  )}
                </div>
              
                <h2 className="text-xl font-medium text-neutral-500">Add up to 5 Documents (Optional)</h2>
              
                <div className={cn(
                  (form.watch("documents.documents")?.length || 0) === 0
                    ? "w-full" // Full width container when no documents
                    : "lg:w-[37vw] xl:w-[47vw] grid grid-cols-2 gap-4" // Grid layout when documents exist
                )}>
                
                  {form.watch("documents.documents")?.map((file, index) => {
                    return (
                      <div key={index} className="relative rounded-lg overflow-hidden h-24 bg-gray-100 flex items-center p-3">
                        <div className="bg-gray-200 p-2 rounded-full mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div className="flex-1 truncate">
                          <p className="text-sm font-medium truncate">{file.name}</p>
                          <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                      
                        <button
                                                    type="button"
                                                    className="absolute top-2 right-2 bg-black bg-opacity-60 text-white rounded-full p-1 hover:bg-opacity-80"
                                                    onClick={() => {
                                                      const files = form.getValues("documents.documents");
                                                      const newFiles = files.filter((_, i) => i !== index);
                                                      form.setValue("documents.documents", newFiles);
                                                      form.trigger("documents.documents");
                                                    }}
                                                  >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                  </button>
                                                </div>
                                              );
                                            })}
                                          
                                            {(form.watch("documents.documents")?.length || 0) < 5 && (
                                              <div className={cn(
                                                "border-2 border-dashed border-purple-200 rounded-lg bg-purple-50 flex flex-col items-center justify-center text-center",
                                                (form.watch("documents.documents")?.length || 0) === 0
                                                  ? "p-10" // Larger padding when standalone
                                                  : "h-24 p-4" // Fixed height when in grid
                                              )}>
                                                <div className={cn(
                                                  "bg-purple-500 rounded-full text-white",
                                                  (form.watch("documents.documents")?.length || 0) === 0
                                                    ? "p-4 mb-4" // Larger icon when standalone
                                                    : "hidden" // Smaller icon when in grid
                                                )}>
                                                  <svg xmlns="http://www.w3.org/2000/svg"
                                                    className={cn(
                                                      (form.watch("documents.documents")?.length || 0) === 0
                                                        ? "h-8 w-8" // Larger icon when standalone
                                                        : "hidden" // Smaller icon when in grid
                                                    )}
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                  >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                  </svg>
                                                </div>
                                               
                                                <p className={cn(
                                                  "text-purple-600 font-medium",
                                                  (form.watch("documents.documents")?.length || 0) === 0
                                                    ? "text-base mb-2" // Larger text when standalone
                                                    : "text-sm mb-1" // Smaller text when in grid
                                                )}>
                                                  {(form.watch("documents.documents")?.length || 0) === 0
                                                    ? "Add relevant project documents"
                                                    : ""}
                                                </p>
                                               
                                                {(form.watch("documents.documents")?.length || 0) === 0 && (
                                                <p className={cn(
                                                  "text-gray-400 text-sm mb-4"
                                                )}>
                                                  Formats: PDF, DOC, DOCX | Max size: 15 MB
                                                </p>
                                                )}
                                               
                                                <label htmlFor="document-upload" className={cn(
                                                  "inline-block border border-gray-300 rounded-md text-gray-700 bg-white cursor-pointer hover:bg-gray-50",
                                                  (form.watch("documents.documents")?.length || 0) === 0
                                                    ? "px-6 py-2 text-base" // Larger button when standalone
                                                    : "px-2 py-1 text-sm" // Smaller button when in grid
                                                )}>
                                                  Upload Documents
                                                  <input
                                                    id="document-upload"
                                                    type="file"
                                                    multiple
                                                    accept=".pdf,.doc,.docx"
                                                    className="sr-only"
                                                    onChange={(e) => {
                                                      if (e.target.files) {
                                                        const files = Array.from(e.target.files);
                                                        const currentFiles = form.getValues("documents.documents") || [];
                                                       
                                                        const validFiles = files.filter(file =>
                                                          ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
                                                        );
                                                       
                                                        const sizeValidFiles = validFiles.filter(file =>
                                                          file.size <= 15 * 1024 * 1024
                                                        );
                                                       
                                                        const newFiles = [...currentFiles, ...sizeValidFiles].slice(0, 5);
                                                       
                                                        form.setValue("documents.documents", newFiles);
                                                        form.trigger("documents.documents");
                                                        e.target.value = ''; // Reset input
                                                      }
                                                    }}
                                                  />
                                                </label>
                                              </div>
                                            )}
                                          </div>
                                         
                                          <p className="text-sm text-gray-500">
                                            {(form.watch("documents.documents")?.length || 0)} of 5 documents uploaded
                                          </p>
                                        </div>
                                      </MultiStepFormStep>
                          
                                      {/* Step 5: Review */}
                                      <MultiStepFormStep name="review">
                                          <div className="space-y-8 font-inter">
                                            <div className="space-y-8">
                                             
                                                <h2 className="font-semibold text-lg text-gray-700 my-4">Basic Details</h2>
                                                <dl className="grid grid-cols-1 gap-y-5">
                                                  <div>
                                                    <dt className="text-sm mb-[6px] text-neutral-500">State</dt>
                                                    <dd className="text-md">{form.watch('basicInfo.state')}</dd>
                                                  </div>
                                                  <div>
                                                    <dt className="text-sm mb-[6px] text-neutral-500">City</dt>
                                                    <dd className="text-md">{form.watch('basicInfo.city')}</dd>
                                                  </div>
                                                  <div>
                                                    <dt className="text-sm mb-[6px] text-neutral-500">Pincode</dt>
                                                    <dd className="text-md">{form.watch('basicInfo.pincode')}</dd>
                                                  </div>
                                                  <div>
                                                    <dt className="text-sm mb-[6px] text-neutral-500">Address</dt>
                                                    <dd className="text-md">{form.watch('basicInfo.address')}</dd>
                                                  </div>
                                                  <div>
                                                    <dt className="text-sm mb-[6px] text-neutral-500">Building / Society / Project (Optional)</dt>
                                                    <dd className="text-md">{form.watch('basicInfo.landIdentifier') ? `${form.watch('basicInfo.landIdentifier')}` : 'Not specified'}</dd>
                                                  </div>
                                                </dl>
                          
                                             
                                            <h2 className="font-semibold text-lg text-gray-700 my-4">Project Details</h2>
                                            <dl className="grid grid-cols-1 gap-y-5">
                                              <div>
                                                <dt className="text-sm mb-[6px] text-neutral-500">Project Type</dt>
                                                <dd className="text-md">
                                                  {form.watch('details.projectType') || 'Not specified'}
                                                </dd>
                                              </div>
                                              <div>
                                                <dt className="text-sm mb-[6px] text-neutral-500">Land Type</dt>
                                                <dd className="text-md">
                                                  {form.watch('details.landType') || 'Not specified'}
                                                </dd>
                                              </div>
                                              <div>
                                                <dt className="text-sm mb-[6px] text-neutral-500">Land Area</dt>
                                                <dd className="text-md">
                                                  {form.watch('details.area') ? `${form.watch('details.area')} sq ft` : 'Not specified'}
                                                </dd>
                                              </div>
                                              <div>
                                                <dt className="text-sm mb-[6px] text-neutral-500">Budget</dt>
                                                <dd className="text-md">{form.watch('details.price') || 'Not specified'}</dd>
                                              </div>
                          
                                              <h2 className="font-semibold text-lg text-gray-700 my-4">Project Description</h2>
                          
                                              <div>
                                                <dt className="text-sm mb-[6px] font-medium text-gray-500">Project Title</dt>
                                                <dd className="text-md">{form.watch('details.title') || 'Not specified'}</dd>
                                              </div>
                                              <div>
                                                <dt className="text-sm mb-[6px] font-medium text-gray-500">Project Description</dt>
                                                <dd className="text-md">{form.watch('details.description') || 'Not specified'}</dd>
                                              </div>
                                            </dl>
                          
                                          <h2 className="font-semibold text-lg text-gray-700 my-4">Project Photos</h2>
                                                         
                                          <div className="">
                                            {form.getValues("photos.images")?.length > 0 ? (
                                              <div className="space-y-2">
                                                {typeof form.getValues("photos.thumbnail") === 'number' && form.getValues("photos.thumbnail") >= 0 && (
                                                  <div className="mb-2">
                                                    <div className="relative rounded-lg overflow-hidden h-40 w-full bg-gray-100">
                                                      <img
                                                        src={URL.createObjectURL(form.getValues("photos.images")[form.getValues("photos.thumbnail")])}
                                                        alt="Thumbnail"
                                                        className="w-full h-full object-cover"
                                                      />
                                                      <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-3 py-1 rounded-md text-sm">
                                                        Thumbnail
                                                      </div>
                                                    </div>
                                                  </div>
                                                )}
                                               
                                                <div className="grid grid-cols-3 gap-2">
                                                  {form.getValues("photos.images").map((file, index) => {
                                                    if (index === form.getValues("photos.thumbnail")) return null;
                                                    return (
                                                      <div key={index} className="relative rounded-lg overflow-hidden h-20 bg-gray-100">
                                                        <img
                                                          src={URL.createObjectURL(file)}
                                                          alt={`Upload ${index + 1}`}
                                                          className="w-full h-full object-cover"
                                                        />
                                                      </div>
                                                    );
                                                  })}
                                                </div>
                                              </div>
                                            ) : (
                                              <p className="text-sm text-gray-500">No photos uploaded</p>
                                            )}
                                          </div>
                          
                                          <h2 className="font-semibold text-lg text-gray-700 my-4">Documents</h2>
                          
                                          <div className="">
                                            {form.getValues("documents.documents")?.length > 0 ? (
                                              <div className="space-y-2">
                                                <div className="grid grid-cols-1 gap-2">
                                                  {form.getValues("documents.documents").map((file, index) => (
                                                    <div key={index} className="flex items-center p-2 bg-white rounded border border-gray-200">
                                                      <div className="bg-gray-100 p-2 rounded-full mr-3">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                      </div>
                                                      <div>
                                                        <p className="text-sm font-medium">{file.name}</p>
                                                        <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                                      </div>
                                                    </div>
                                                  ))}
                                                </div>
                                              </div>
                                            ) : (
                                              <p className="text-sm text-gray-500">No documents uploaded</p>
                                            )}
                                          </div>
                                            </div>
                                           
                                            <div className="flex gap-4">
                                              <button
                                                type="button"
                                                onClick={onSaveAsDraft}
                                                className="flex-1 px-6 py-2 border rounded-full font-inter hover:bg-gray-50"
                                              >
                                                Save as Draft
                                              </button>
                                              <button
                                                type="submit"
                                                className="flex-1 px-6 py-2 bg-orospaces-purple font-inter text-white rounded-full"
                                              >
                                                Post Project
                                              </button>
                                            </div>
                                          </div>
                                        </MultiStepFormStep>
                                         
                                        <MultiStepFormFooter>
                                          <MultiStepFormContextProvider>
                                            {(context) => (
                                              context.currentStep !== 'review' && <FormNavigation />
                                            )}
                                          </MultiStepFormContextProvider>
                                        </MultiStepFormFooter>
                                      </MultiStepForm>
                                      </div>
                                    )}
                                  </ClientOnly>
                                </div>
                              </>
                            );
                          }
                          
                          // Add this TypeScript declaration to make the initMapCallback available globally
                          declare global {
                            interface Window {
                              initMapCallback: () => void;
                              google: any;
                            }
                          }