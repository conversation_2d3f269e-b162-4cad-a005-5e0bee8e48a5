import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { ActionFunctionArgs, LoaderFunctionArgs, redirect } from "@remix-run/node";
import { useState, useEffect } from "react";
import { verifyOTP, resendOTP } from "../services/auth.server";
import { Button } from "../components/ui/button";
import { Loader2 } from "lucide-react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "../components/ui/input-otp";
import { createUserSession } from "../services/session.server";


type ActionData = 
  | { error: string; resent?: boolean }
  | { message: string; resent: boolean }
  | null 
  | undefined;

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const encodedPhone = url.searchParams.get("p");

  if (!encodedPhone) {
    return redirect("/signup");
  }
  
  try {
    const phone = Buffer.from(decodeURIComponent(encodedPhone), 'base64').toString();
    
    const phoneRegex = /^\+91\d{10}$/;
    if (!phoneRegex.test(phone)) {
      return redirect("/signup?error=invalid_phone");
    }
    
    return { phone };
  } catch (_) {
    return redirect("/signup?error=invalid_phone");
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");
  let phone = formData.get("phone") as string;

  let digits = phone.replace(/^\+/, '').replace(/\D/g, '');
  
  if (!digits.startsWith('91')) {
    digits = '91' + digits;
  }
  phone = '+' + digits;
  
  const phoneRegex = /^\+91\d{10}$/;
  if (!phoneRegex.test(phone)) {
    return { error: "Invalid phone number" } as const;
  }

  if (intent === "resend") {
    try {
      const result = await resendOTP(phone);
      
      if (!result.success) {
        return { error: result.error ?? "Failed to resend OTP", resent: false } as const;
      }
      
      return { message: "OTP resent successfully", resent: true } as const;
    } catch (error) {
      console.error("Error resending OTP:", error);
      return { error: "Failed to resend OTP", resent: false } as const;
    }
  } else {
    try {
      const totp = formData.get("totp") as string;
      const result = await verifyOTP(phone, totp);

      if (result.success && result.token) {
        return await createUserSession(result.token, "/");
      } else {
        return { error: result.error || "OTP verification failed. Please try again." } as const;
      }
    } catch (error) {
      return { 
        error: error instanceof Error ? error.message : "An unexpected error occurred during OTP verification." 
      } as const;
    }
  }
}

export default function VerificationPage() {
  const { phone } = useLoaderData<typeof loader>();
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isLoading = navigation.state === "submitting";
  const [totp, setTotp] = useState("");
  const [remainingTime, setRemainingTime] = useState(60);
  const [canResend, setCanResend] = useState(false);

  // Timer for OTP resend
  useEffect(() => {
    if (remainingTime > 0 && !canResend) {
      const timer = setTimeout(() => {
        setRemainingTime(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [remainingTime, canResend]);

  // Reset timer when OTP is resent
  useEffect(() => {
    if (actionData && 'resent' in actionData && actionData.resent) {
      setRemainingTime(60);
      setCanResend(false);
    }
  }, [actionData]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-6 p-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-2">Verify Your Phone Number</h1>
          <p className="text-gray-600">
            We've sent a verification code to
            <span className="font-medium"> {phone}</span>
          </p>
        </div>

        {actionData && 'error' in actionData && (
          <div className="p-3 text-sm text-red-500 bg-red-50 rounded-md">
            {actionData.error}
          </div>
        )}

        {actionData && 'message' in actionData && (
          <div className="p-3 text-sm text-green-500 bg-green-50 rounded-md">
            {actionData.message}
          </div>
        )}

        <Form method="post" className="space-y-6">
          <input type="hidden" name="phone" value={phone} />
          
          <div className="flex flex-col items-center gap-4">
            <label htmlFor="totp" className="text-sm text-gray-600">
              Enter 6-digit verification code
            </label>
            
            <InputOTP maxLength={6} name="totp" value={totp} onChange={setTotp}>
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>
          
          <Button
            type="submit"
            className="w-full h-10 bg-orospaces-purple hover:bg-orospaces-purple/90 text-white"
            disabled={totp.length !== 6 || isLoading}
          >
            {isLoading && navigation.formData?.get("intent") !== "resend" ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify OTP"
            )}
          </Button>
        </Form>

        <div className="text-center">
          <p className="text-sm text-gray-600 mb-2">
            Didn't receive the code?
          </p>
          
          <Form method="post">
            <input type="hidden" name="intent" value="resend" />
            <input type="hidden" name="phone" value={phone} />
            
            <Button
              type="submit"
              variant="link"
              className="text-orospaces-purple hover:text-orospaces-purple/90"
              disabled={!canResend || (isLoading && navigation.formData?.get("intent") === "resend")}
            >
              {isLoading && navigation.formData?.get("intent") === "resend" ? (
                <>
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  Resending...
                </>
              ) : !canResend ? (
                `Resend OTP (${remainingTime}s)`
              ) : (
                "Resend OTP"
              )}
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
}