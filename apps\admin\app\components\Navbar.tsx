import React from "react";
import { useState } from "react";
import { Link, useLocation } from '@remix-run/react';
import { But<PERSON> } from "./ui/button";
import { Avatar, AvatarImage } from "./ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { LogOut } from 'lucide-react';
import ProfileSvg from "../assets/profile";
import HomeSvg from "../assets/home";
import FolderSvg from "../assets/folder";
import SettingsSvg from "../assets/settings";

interface MenuItem {
    label: string;
    icon: React.ReactNode;
    href: string;
  }

const menuItems: MenuItem[] = [
    { label: 'Dashboard', icon: <HomeSvg />, href: '/' },
    { label: 'Manage Ads', icon: <FolderSvg />, href: '/projects' },
    { label: 'Manage Users', icon: <ProfileSvg />, href: '/users' },
    { label: 'Enquiries', icon: <SettingsSvg />, href: '/enquiries' },
    { label: 'Logout', icon: <LogOut />, href: '/logout' },
    ];

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const notificationItems = [
    { key: "test1", label: "Notification 1" },
    { key: "test2", label: "Notification 2" },
    { key: "test3", label: "Notification 3" },
    { key: "test4", label: "Notification 4" }
  ];

  return (
    <>
      <nav className="flex items-center justify-between bg-white px-3 md:px-7 py-2 h-16 shadow-md z-20 relative ">
        <div className="flex items-center gap-2 md:gap-4">
          <div className="block md:hidden">
            <svg
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="hover:cursor-pointer"
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="28"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.25"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M3 12h18" />
              <path d="M3 18h18" />
              <path d="M3 6h18" />
            </svg>
          </div>
          <svg width="151" height="32" viewBox="0 0 151 32" fill="none" xmlns="http://www.w3.org/2000/svg">
         <path d="M51.78 24.24C50.6867 24.24 49.6667 24.0467 48.72 23.66C47.7733 23.2733 46.9467 22.7333 46.24 22.04C45.5333 21.3467 44.98 20.5333 44.58 19.6C44.1933 18.6533 44 17.6333 44 16.54C44 15.4333 44.1933 14.4133 44.58 13.48C44.9667 12.5467 45.5133 11.7333 46.22 11.04C46.9267 10.3467 47.7533 9.81333 48.7 9.44C49.6467 9.05333 50.6733 8.86 51.78 8.86C52.8867 8.86 53.9133 9.05333 54.86 9.44C55.8067 9.82667 56.6333 10.3667 57.34 11.06C58.0467 11.74 58.5933 12.5467 58.98 13.48C59.38 14.4133 59.58 15.4333 59.58 16.54C59.58 17.6333 59.38 18.6533 58.98 19.6C58.58 20.5333 58.0267 21.3467 57.32 22.04C56.6133 22.7333 55.7867 23.2733 54.84 23.66C53.9067 24.0467 52.8867 24.24 51.78 24.24ZM51.78 21.76C52.5133 21.76 53.1867 21.6333 53.8 21.38C54.4133 21.1133 54.9467 20.7467 55.4 20.28C55.8667 19.8133 56.22 19.26 56.46 18.62C56.7133 17.98 56.84 17.2867 56.84 16.54C56.84 15.7933 56.7133 15.1067 56.46 14.48C56.22 13.84 55.8667 13.2867 55.4 12.82C54.9467 12.34 54.4133 11.9733 53.8 11.72C53.1867 11.4667 52.5133 11.34 51.78 11.34C51.06 11.34 50.3933 11.4667 49.78 11.72C49.1667 11.9733 48.6267 12.34 48.16 12.82C47.7067 13.2867 47.3533 13.84 47.1 14.48C46.8467 15.1067 46.72 15.7933 46.72 16.54C46.72 17.2867 46.8467 17.98 47.1 18.62C47.3533 19.26 47.7067 19.8133 48.16 20.28C48.6267 20.7467 49.1667 21.1133 49.78 21.38C50.3933 21.6333 51.06 21.76 51.78 21.76ZM61.7786 24V13.12H64.2386V15.54L64.0386 15.18C64.2919 14.3667 64.6853 13.8 65.2186 13.48C65.7653 13.16 66.4186 13 67.1786 13H67.8186V15.32H66.8786C66.1319 15.32 65.5319 15.5533 65.0786 16.02C64.6253 16.4733 64.3986 17.1133 64.3986 17.94V24H61.7786ZM74.5791 24.24C73.5124 24.24 72.5391 23.9933 71.6591 23.5C70.7924 23.0067 70.0991 22.3333 69.5791 21.48C69.0724 20.6267 68.8191 19.6533 68.8191 18.56C68.8191 17.4667 69.0724 16.4933 69.5791 15.64C70.0991 14.7867 70.7924 14.1133 71.6591 13.62C72.5257 13.1267 73.4991 12.88 74.5791 12.88C75.6457 12.88 76.6124 13.1267 77.4791 13.62C78.3457 14.1133 79.0324 14.7867 79.5391 15.64C80.0591 16.48 80.3191 17.4533 80.3191 18.56C80.3191 19.6533 80.0591 20.6267 79.5391 21.48C79.0191 22.3333 78.3257 23.0067 77.4591 23.5C76.5924 23.9933 75.6324 24.24 74.5791 24.24ZM74.5791 21.84C75.1657 21.84 75.6791 21.7 76.1191 21.42C76.5724 21.14 76.9257 20.7533 77.1791 20.26C77.4457 19.7533 77.5791 19.1867 77.5791 18.56C77.5791 17.92 77.4457 17.36 77.1791 16.88C76.9257 16.3867 76.5724 16 76.1191 15.72C75.6791 15.4267 75.1657 15.28 74.5791 15.28C73.9791 15.28 73.4524 15.4267 72.9991 15.72C72.5457 16 72.1857 16.3867 71.9191 16.88C71.6657 17.36 71.5391 17.92 71.5391 18.56C71.5391 19.1867 71.6657 19.7533 71.9191 20.26C72.1857 20.7533 72.5457 21.14 72.9991 21.42C73.4524 21.7 73.9791 21.84 74.5791 21.84ZM86.4459 24.24C85.2859 24.24 84.2726 23.9667 83.4059 23.42C82.5526 22.86 81.9659 22.1067 81.6459 21.16L83.6059 20.22C83.8859 20.8333 84.2726 21.3133 84.7659 21.66C85.2726 22.0067 85.8326 22.18 86.4459 22.18C86.9259 22.18 87.3059 22.0733 87.5859 21.86C87.8659 21.6467 88.0059 21.3667 88.0059 21.02C88.0059 20.8067 87.9459 20.6333 87.8259 20.5C87.7193 20.3533 87.5659 20.2333 87.3659 20.14C87.1793 20.0333 86.9726 19.9467 86.7459 19.88L84.9659 19.38C84.0459 19.1133 83.3459 18.7067 82.8659 18.16C82.3993 17.6133 82.1659 16.9667 82.1659 16.22C82.1659 15.5533 82.3326 14.9733 82.6659 14.48C83.0126 13.9733 83.4859 13.58 84.0859 13.3C84.6993 13.02 85.3993 12.88 86.1859 12.88C87.2126 12.88 88.1193 13.1267 88.9059 13.62C89.6926 14.1133 90.2526 14.8067 90.5859 15.7L88.5859 16.64C88.3993 16.1467 88.0859 15.7533 87.6459 15.46C87.2059 15.1667 86.7126 15.02 86.1659 15.02C85.7259 15.02 85.3793 15.12 85.1259 15.32C84.8726 15.52 84.7459 15.78 84.7459 16.1C84.7459 16.3 84.7993 16.4733 84.9059 16.62C85.0126 16.7667 85.1593 16.8867 85.3459 16.98C85.5459 17.0733 85.7726 17.16 86.0259 17.24L87.7659 17.76C88.6593 18.0267 89.3459 18.4267 89.8259 18.96C90.3193 19.4933 90.5659 20.1467 90.5659 20.92C90.5659 21.5733 90.3926 22.1533 90.0459 22.66C89.6993 23.1533 89.2193 23.54 88.6059 23.82C87.9926 24.1 87.2726 24.24 86.4459 24.24ZM92.5794 28V13.12H95.0394V15.28L94.7994 14.74C95.1727 14.1533 95.6794 13.7 96.3194 13.38C96.9594 13.0467 97.6994 12.88 98.5394 12.88C99.566 12.88 100.493 13.1333 101.319 13.64C102.146 14.1467 102.799 14.8267 103.279 15.68C103.773 16.5333 104.019 17.4933 104.019 18.56C104.019 19.6133 103.779 20.5733 103.299 21.44C102.819 22.3067 102.166 22.9933 101.339 23.5C100.513 23.9933 99.5727 24.24 98.5194 24.24C97.7327 24.24 97.006 24.0867 96.3394 23.78C95.686 23.46 95.166 23.0067 94.7794 22.42L95.1994 21.9V28H92.5794ZM98.2194 21.84C98.8194 21.84 99.3527 21.7 99.8194 21.42C100.286 21.14 100.646 20.7533 100.899 20.26C101.166 19.7667 101.299 19.2 101.299 18.56C101.299 17.92 101.166 17.36 100.899 16.88C100.646 16.3867 100.286 16 99.8194 15.72C99.3527 15.4267 98.8194 15.28 98.2194 15.28C97.646 15.28 97.126 15.42 96.6594 15.7C96.206 15.98 95.846 16.3733 95.5794 16.88C95.326 17.3733 95.1994 17.9333 95.1994 18.56C95.1994 19.2 95.326 19.7667 95.5794 20.26C95.846 20.7533 96.206 21.14 96.6594 21.42C97.126 21.7 97.646 21.84 98.2194 21.84ZM109.317 24.24C108.557 24.24 107.897 24.1133 107.337 23.86C106.777 23.6067 106.344 23.2467 106.037 22.78C105.73 22.3 105.577 21.7467 105.577 21.12C105.577 20.52 105.71 19.9867 105.977 19.52C106.244 19.04 106.657 18.64 107.217 18.32C107.777 18 108.484 17.7733 109.337 17.64L112.897 17.06V19.06L109.837 19.58C109.317 19.6733 108.93 19.84 108.677 20.08C108.424 20.32 108.297 20.6333 108.297 21.02C108.297 21.3933 108.437 21.6933 108.717 21.92C109.01 22.1333 109.37 22.24 109.797 22.24C110.344 22.24 110.824 22.1267 111.237 21.9C111.664 21.66 111.99 21.3333 112.217 20.92C112.457 20.5067 112.577 20.0533 112.577 19.56V16.76C112.577 16.2933 112.39 15.9067 112.017 15.6C111.657 15.28 111.177 15.12 110.577 15.12C110.017 15.12 109.517 15.2733 109.077 15.58C108.65 15.8733 108.337 16.2667 108.137 16.76L105.997 15.72C106.21 15.1467 106.544 14.6533 106.997 14.24C107.464 13.8133 108.01 13.48 108.637 13.24C109.264 13 109.944 12.88 110.677 12.88C111.57 12.88 112.357 13.0467 113.037 13.38C113.717 13.7 114.244 14.1533 114.617 14.74C115.004 15.3133 115.197 15.9867 115.197 16.76V24H112.717V22.14L113.277 22.1C112.997 22.5667 112.664 22.96 112.277 23.28C111.89 23.5867 111.45 23.8267 110.957 24C110.464 24.16 109.917 24.24 109.317 24.24ZM122.858 24.24C121.778 24.24 120.805 23.9933 119.938 23.5C119.085 22.9933 118.412 22.3133 117.918 21.46C117.425 20.5933 117.178 19.62 117.178 18.54C117.178 17.46 117.425 16.4933 117.918 15.64C118.412 14.7867 119.085 14.1133 119.938 13.62C120.805 13.1267 121.778 12.88 122.858 12.88C123.632 12.88 124.352 13.02 125.018 13.3C125.685 13.5667 126.258 13.94 126.738 14.42C127.232 14.8867 127.585 15.4467 127.798 16.1L125.498 17.1C125.298 16.5533 124.958 16.1133 124.478 15.78C124.012 15.4467 123.472 15.28 122.858 15.28C122.285 15.28 121.772 15.42 121.318 15.7C120.878 15.98 120.532 16.3667 120.278 16.86C120.025 17.3533 119.898 17.92 119.898 18.56C119.898 19.2 120.025 19.7667 120.278 20.26C120.532 20.7533 120.878 21.14 121.318 21.42C121.772 21.7 122.285 21.84 122.858 21.84C123.485 21.84 124.032 21.6733 124.498 21.34C124.965 21.0067 125.298 20.56 125.498 20L127.798 21.04C127.585 21.6533 127.238 22.2067 126.758 22.7C126.278 23.18 125.705 23.56 125.038 23.84C124.372 24.1067 123.645 24.24 122.858 24.24ZM134.946 24.24C133.826 24.24 132.846 23.9867 132.006 23.48C131.166 22.9733 130.513 22.2867 130.046 21.42C129.58 20.5533 129.346 19.5933 129.346 18.54C129.346 17.4467 129.58 16.48 130.046 15.64C130.526 14.7867 131.173 14.1133 131.986 13.62C132.813 13.1267 133.733 12.88 134.746 12.88C135.6 12.88 136.346 13.02 136.986 13.3C137.64 13.58 138.193 13.9667 138.646 14.46C139.1 14.9533 139.446 15.52 139.686 16.16C139.926 16.7867 140.046 17.4667 140.046 18.2C140.046 18.3867 140.033 18.58 140.006 18.78C139.993 18.98 139.96 19.1533 139.906 19.3H131.506V17.3H138.426L137.186 18.24C137.306 17.6267 137.273 17.08 137.086 16.6C136.913 16.12 136.62 15.74 136.206 15.46C135.806 15.18 135.32 15.04 134.746 15.04C134.2 15.04 133.713 15.18 133.286 15.46C132.86 15.7267 132.533 16.1267 132.306 16.66C132.093 17.18 132.013 17.8133 132.066 18.56C132.013 19.2267 132.1 19.82 132.326 20.34C132.566 20.8467 132.913 21.24 133.366 21.52C133.833 21.8 134.366 21.94 134.966 21.94C135.566 21.94 136.073 21.8133 136.486 21.56C136.913 21.3067 137.246 20.9667 137.486 20.54L139.606 21.58C139.393 22.1 139.06 22.56 138.606 22.96C138.153 23.36 137.613 23.6733 136.986 23.9C136.373 24.1267 135.693 24.24 134.946 24.24ZM146.173 24.24C145.013 24.24 143.999 23.9667 143.133 23.42C142.279 22.86 141.693 22.1067 141.373 21.16L143.333 20.22C143.613 20.8333 143.999 21.3133 144.493 21.66C144.999 22.0067 145.559 22.18 146.173 22.18C146.653 22.18 147.033 22.0733 147.313 21.86C147.593 21.6467 147.733 21.3667 147.733 21.02C147.733 20.8067 147.673 20.6333 147.553 20.5C147.446 20.3533 147.293 20.2333 147.093 20.14C146.906 20.0333 146.699 19.9467 146.473 19.88L144.693 19.38C143.773 19.1133 143.073 18.7067 142.593 18.16C142.126 17.6133 141.893 16.9667 141.893 16.22C141.893 15.5533 142.059 14.9733 142.393 14.48C142.739 13.9733 143.213 13.58 143.813 13.3C144.426 13.02 145.126 12.88 145.913 12.88C146.939 12.88 147.846 13.1267 148.633 13.62C149.419 14.1133 149.979 14.8067 150.313 15.7L148.313 16.64C148.126 16.1467 147.813 15.7533 147.373 15.46C146.933 15.1667 146.439 15.02 145.893 15.02C145.453 15.02 145.106 15.12 144.853 15.32C144.599 15.52 144.473 15.78 144.473 16.1C144.473 16.3 144.526 16.4733 144.633 16.62C144.739 16.7667 144.886 16.8867 145.073 16.98C145.273 17.0733 145.499 17.16 145.753 17.24L147.493 17.76C148.386 18.0267 149.073 18.4267 149.553 18.96C150.046 19.4933 150.293 20.1467 150.293 20.92C150.293 21.5733 150.119 22.1533 149.773 22.66C149.426 23.1533 148.946 23.54 148.333 23.82C147.719 24.1 146.999 24.24 146.173 24.24Z" fill="#100A55"/>
         <g clipPath="url(#clip0_1281_9675)">
         <path d="M30.0426 11.6137L21.6101 6.08301C20.87 5.59755 19.9592 5.33398 19.0216 5.33398C18.0841 5.33398 17.1732 5.59755 16.4332 6.08301L7.99912 11.6137C7.49226 11.9461 7.08218 12.3717 6.80019 12.8581C6.5182 13.3446 6.37177 13.8789 6.37207 14.4203V24.0203C6.37207 24.7276 6.70525 25.4059 7.29831 25.906C7.89138 26.4061 8.69575 26.687 9.53446 26.687H28.5088C29.3475 26.687 30.1519 26.4061 30.745 25.906C31.338 25.4059 31.6712 24.7276 31.6712 24.0203V14.4203C31.6712 13.323 31.0704 12.287 30.0426 11.6137Z" fill="#7065F0" stroke="#7065F0" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
         <path d="M25.299 20C21.8045 21.7773 16.1407 21.7773 12.6494 20" stroke="#F9FAFB" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
         </g>
         <defs>
         <clipPath id="clip0_1281_9675">
         <rect width="37.9487" height="32" fill="white"/>
         </clipPath>
         </defs>
        </svg>
        </div>
        <div className="flex items-center gap-x-0 sm:gap-x-6 justify-end w-[35vw]">
          <Button asChild className="bg-[#7065F0] hover:bg-[#7065F0] text-white hidden sm:inline-block">
            <Link to="#">Post Project</Link>
          </Button>
          <div className="hidden sm:inline-block">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <svg className="cursor-pointer" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clipPath="url(#clip0_727_10739)">
              <path d="M26.8571 20.459L25.4682 18.1534C25.1765 17.6395 24.9126 16.6672 24.9126 16.0978V12.5838C24.9126 9.31982 22.9959 6.5003 20.2319 5.18083C19.5097 3.90301 18.1763 3.11133 16.6485 3.11133C15.1346 3.11133 13.7734 3.93079 13.0512 5.22249C10.3428 6.56975 8.46776 9.36149 8.46776 12.5838V16.0978C8.46776 16.6672 8.20386 17.6395 7.91219 18.1395L6.50937 20.459C5.9538 21.3896 5.8288 22.4174 6.17603 23.3618C6.50937 24.2924 7.30106 25.0147 8.32886 25.3619C11.0234 26.2786 13.8568 26.723 16.6902 26.723C19.5236 26.723 22.357 26.2786 25.0515 25.3758C26.0238 25.0563 26.7738 24.3202 27.1349 23.3618C27.496 22.4035 27.3988 21.3479 26.8571 20.459Z" fill="#333333"/>
              <path d="M20.5945 28.1257C20.0111 29.7369 18.4694 30.8897 16.6638 30.8897C15.5666 30.8897 14.4832 30.4452 13.7193 29.6535C13.2749 29.2368 12.9415 28.6813 12.7471 28.1118C12.9276 28.1396 13.1082 28.1535 13.3026 28.1813C13.6221 28.2229 13.9554 28.2646 14.2888 28.2924C15.0805 28.3618 15.886 28.4035 16.6916 28.4035C17.4833 28.4035 18.275 28.3618 19.0528 28.2924C19.3445 28.2646 19.6361 28.2507 19.9139 28.209C20.1361 28.1813 20.3584 28.1535 20.5945 28.1257Z" fill="#333333"/>
              </g>
              <rect x="24.9941" y="0.333496" width="8.33354" height="8.33354" rx="4.16677" fill="#DF3020"/>
              <defs>
              <clipPath id="clip0_727_10739">
              <rect width="33.3342" height="33.3342" fill="white" transform="translate(-0.00390625 0.333496)"/>
              </clipPath>
              </defs>
            </svg>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {notificationItems.map((item) => (
                <DropdownMenuItem key={item.key}>{item.label}</DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          </div>
          <Avatar className="cursor-pointer border-3 border-[#7065F0]">
            <AvatarImage src="https://i.pravatar.cc/150?u=a042581f4e29026704d" alt="@user" />
          </Avatar>
        </div>
      </nav>
      <div
        className={`fixed top-16 left-0 w-[55vw] p-2 h-[calc(100vh-64px)] bg-white border-r-1 border-gray-200 transform transition-transform duration-300 ease-in-out ${
          isMenuOpen ? 'translate-x-0' : '-translate-x-full'
        } md:hidden z-10`}
      >
        <div className=" px-2 py-3  ">
          {menuItems.map((item, index) => (
            <div
            key={`${item.label}-${index}`} className={`rounded-md p-2 w-full mb-2 flex justify-start items-center ${index === 4 ? 'mt-20 text-red-500 ' : 'hover:bg-gradient-to-r from-indigo-500 to-[#CFCCFA] hover:text-white'}`}
            >
              <Link
                className={`w-full flex justify-start gap-x-2 ${index === 4 ? 'justify-center' : ''}`}
                to={item.href}
              >
                <span className="text-xl">{item.icon}</span>
                <span className="font-medium">{item.label}</span>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </>

  );
}

interface MenuItemProps {
    item: MenuItem;
    index: number;
    isActive: boolean;
  }
  
  const MenuItem = React.memo(function MenuItem({ item, index, isActive }: MenuItemProps) {
    const baseClasses = "rounded-lg mb-2 px-1 py-2 lg:p-3 w-full flex justify-start items-center transition-colors duration-200";
    const activeClasses = "bg-gradient-to-r from-indigo-500/90 from-10% to-[#CFCCFA]/90 to-100% text-white";
    const hoverClasses = "hover:bg-gradient-to-r from-indigo-500/90 from-10% to-[#CFCCFA]/90 to-100% hover:text-white";
    
    return (
      <div 
        className={`${baseClasses} ${
          index === 4 ? 'mt-12 bg-transparent' : hoverClasses
        } ${isActive ? activeClasses : ""}`}
      >
        <Link
          to={item.href}
          className={`w-full flex justify-start items-center gap-x-2 xl:gap-x-3 text-medium lg:text-base ${
            index === 4 ? 'text-red-500 justify-center' : ''
          }`}
        >
          <span className="text-xl">{item.icon}</span>
          {item.label}
        </Link>
      </div>
    );
  });

  export const Sidebar = React.memo(function Sidebar() {
    const location = useLocation();
    
    const isActivePath = React.useCallback((path: string) => {
      return location.pathname === path;
    }, [location.pathname]);
  
    return (
      <aside className="hidden font-medium md:block w-1/5 bg-white shadow-lg border-r border-gray-100 px-2 xl:px-3 pt-8 sticky top-16 h-[calc(100vh - 64px)] z-10">
        {menuItems.map((item, index) => (
          <MenuItem 
            key={`${item.label}-${index}`}
            item={item}
            index={index}
            isActive={isActivePath(item.href)}
          />
        ))}
      </aside>
    );
  });


      

        
    

    