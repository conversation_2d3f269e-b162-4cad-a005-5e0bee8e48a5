import type { User } from "../services/types";
import { getApiToken } from "../services/session.server";
import { redirect } from "@remix-run/node";

const API_URL = "http://localhost:4000";

export async function getCurrentUser(request: Request): Promise<User | null> {
  const apiToken = await getApiToken(request);
  if (!apiToken) {
    return null;
  }

  try {
    const response = await fetch(`${API_URL}/user/me`, {
      headers: {
        "Cookie": `token=${apiToken}`,
        "Content-Type": "application/json",
      },
    });
    
    if (!response.ok) {
      const errorBody = await response.text();
      console.error(`Failed to fetch user from API (${response.status}) using token in Cookie header: ${errorBody}`);
      return null;
    }
    
    const responseData = await response.json();
    if (responseData && responseData.user) {
      return responseData.user as User;
    } else {
      console.error("User data not found in API response (called with token in Cookie header):", responseData);
      return null;
    }
  } catch (error) {
    console.error("Error fetching user data (token in Cookie header):", error);
    return null;
  }
}

export async function requireAuth(request: Request) {
  const user = await getCurrentUser(request);
  
  if (!user) {
    throw new Response("Unauthorized", {
      status: 401,
      statusText: "Unauthorized"
    });
  }
  
  return user;
}

export { getApiToken };
