// app/components/home/<USER>
import { useState, useEffect } from "react";
import type { FormEvent } from "react";
import { Link } from "@remix-run/react";

// Types for dropdown data
interface LocationOption {
  id: string;
  name: string;
}

interface ProjectTypeOption {
  id: string;
  name: string;
}

interface LandSizeOption {
  id: string;
  name: string;
  minSize: number;
  maxSize: number | null;
}

interface HomeProps {
  initialData?: {
    featuredLocations?: LocationOption[];
    featuredProjectTypes?: ProjectTypeOption[];
  };
}

const Home = ({ initialData }: HomeProps) => {
  // State for form inputs
  const [selectedLocation, setSelectedLocation] = useState<string>("");
  const [selectedProjectType, setSelectedProjectType] = useState<string>("");
  const [selectedLandSize, setSelectedLandSize] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // Sample data for dropdowns
  // In production, these would come from API calls
  const locations: LocationOption[] = [
    { id: "loc1", name: "New York" },
    { id: "loc2", name: "Los Angeles" },
    { id: "loc3", name: "Chicago" },
    { id: "loc4", name: "Houston" },
    { id: "loc5", name: "Miami" },
  ];
  
  const projectTypes: ProjectTypeOption[] = [
    { id: "pt1", name: "Residential Building" },
    { id: "pt2", name: "Commercial Complex" },
    { id: "pt3", name: "Mixed-Use Development" },
    { id: "pt4", name: "Industrial Facility" },
    { id: "pt5", name: "Infrastructure Project" },
  ];
  
  const landSizes: LandSizeOption[] = [
    { id: "ls1", name: "< 1,000 sq ft", minSize: 0, maxSize: 1000 },
    { id: "ls2", name: "1,000 - 5,000 sq ft", minSize: 1000, maxSize: 5000 },
    { id: "ls3", name: "5,000 - 20,000 sq ft", minSize: 5000, maxSize: 20000 },
    { id: "ls4", name: "20,000 - 100,000 sq ft", minSize: 20000, maxSize: 100000 },
    { id: "ls5", name: "> 100,000 sq ft", minSize: 100000, maxSize: null },
  ];
  
  // Mock function for search submission
  // Would be connected to backend API in production
  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    
    // This would be replaced with actual API call
    console.log({
      location: selectedLocation,
      projectType: selectedProjectType,
      landSize: selectedLandSize,
      searchQuery
    });
    
    // In production, this would navigate to search results or
    // update the current page with results
  };
  
  return (
    <>
    <div className="w-full h-full flex flex-col ">
  
  {/* Hero Section with Background Image */}
  <div 
    className="relative w-full flex-grow flex flex-col items-center justify-center text-center px-4 py-20"
  >
    {/* Background image container (positioned absolutely) */}
    <div 
      className="absolute inset-0 z-0 bg-[url('/Landing_Page_Banner.svg')] bg-cover bg-center bg-no-repeat"
      aria-hidden="true"
    />
        
        <div className="relative z-10 max-w-4xl mx-auto">
          <h1 className="font-outfit text-4xl md:text-5xl lg:text-6xl font-semibold mb-6">
            <span className="text-gray-800">Connecting </span>
            <span className="text-orospaces-purple">Builders</span>
            <span className="text-gray-800">, </span>
            <span className="text-orospaces-purple">Landowners</span>
            <span className="text-gray-800"> & </span>
            <span className="text-orospaces-purple">Investors</span>
          </h1>
          
          <h2 className="font-outfit text-2xl md:text-3xl lg:text-4xl font-semibold text-gray-800 mb-8">
            for perfect construction partnerships...
          </h2>
          
          <p className="font-inter text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
            Post project ads, partner with experienced builders, and bring ambitious projects to life - all on OroSpaces.
          </p>
          
          {/* Search Section */}
          <div className="max-w-3xl mx-auto">
<div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-3xl md:rounded-full shadow-lg p-6 mb-4">
  {/* Main container with justify-between */}
  <div className="flex flex-wrap justify-between gap-y-5 md:gap-y-0 relative ">
    
    {/* Location Category - First div */}
    <div className="flex-1 min-w-[200px] px-4 border-0 md:border-r border-gray-300 last:border-r-0">
      <div className="flex justify-center items-center">
        <div>
        <div className="mb-2">
            <span className="font-satoshi text-gray-700 font-medium">Location</span>
          </div>
          <div className="relative flex justify-between gap-x-4">
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              className="w-full py-2 font-satoshi appearance-none bg-transparent focus:outline-none text-gray-500"
            >
              <option className="text-start" value="">Select Your City</option>
              {locations.map(location => (
                <option key={location.id} value={location.id} className="text-center">
                  {location.name}
                </option>
              ))}
            </select>
            <div className="inset-y-0 right-0 flex items-center pointer-events-none">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    {/* Project Type Category - Second div */}
    <div className="flex-1 min-w-[200px] px-4 border-0 md:border-r border-gray-300 last:border-r-0">
      <div className="flex justify-center items-center">
        <div>
          <div className="mb-2">
            <span className="font-satoshi text-gray-700 font-medium">Project Type</span>
          </div>
          <div className="relative flex justify-between gap-x-4">
            <select
              value={selectedProjectType}
              onChange={(e) => setSelectedProjectType(e.target.value)}
              className="w-full py-2 font-satoshi appearance-none bg-transparent focus:outline-none text-gray-500"
            >
              <option value="">Select Project Type</option>
              {projectTypes.map(projectType => (
                <option key={projectType.id} value={projectType.id}>
                  {projectType.name}
                </option>
              ))}
            </select>
            <div className="inset-y-0 right-0 flex items-center pointer-events-none">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    {/* Land Size Category - Third div */}
    <div className="flex-1 min-w-[200px] px-4">
      <div className="flex justify-center items-center">
        <div>
          <div className="mb-2">
            <span className="font-satoshi text-gray-700 font-medium">Land Size</span>
          </div>
          <div className="relative flex justify-between gap-x-4">
            <select
              value={selectedLandSize}
              onChange={(e) => setSelectedLandSize(e.target.value)}
              className="w-full py-2 font-satoshi appearance-none bg-transparent focus:outline-none text-gray-500"
            >
              <option value="">Select Land Size</option>
              {landSizes.map(landSize => (
                <option key={landSize.id} value={landSize.id}>
                  {landSize.name}
                </option>
              ))}
            </select>
            <div className="inset-y-0 right-0 flex items-center pointer-events-none">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</div>
            
            {/* Search Bar - Separated from filters with rounded design */}
            <form onSubmit={handleSearch} className="bg-white bg-opacity-80 backdrop-blur-sm rounded-3xl shadow-lg p-2 flex">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none pl-2">
                  <svg className="h-6 w-6 text-orospaces-purple" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-12 pr-3 py-3 font-inter border-none bg-transparent rounded-l-3xl focus:outline-none focus:ring-0 truncate"
                  placeholder="Search for lands, projects, locations..."
                />
              </div>
              <button
                type="submit"
                className="bg-orospaces-purple hover:bg-indigo-600 text-white font-poppins font-medium py-2 px-6 rounded-3xl transition-colors ml-3"
              >
                Search
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    <div className="py-10">
      <p className="text-center py-6 font-inter text-[#777777]">BROWSE POPULAR PROJECT CATEGORIES</p>
      <div className="w-full flex gap-x-6 p-6 overflow-x-scroll scrollbar-hide">
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_1.svg"/>
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_2.svg"/>
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_3.svg"/>
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_4.svg"/>
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_5.svg"/>
        <img className="size-1/2 md:size-1/3 lg:size-[275px]" src="/Frame_6.svg"/>
      </div>
    </div>
    </>
  )
};

export default Home;