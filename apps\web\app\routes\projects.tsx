import { useSearchParams, useLoaderData } from '@remix-run/react';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { Info, Bookmark } from 'lucide-react';
import { useState } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../components/ui/tooltip';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  CarouselImage,
} from '../components/ui/carousel';
import { Menu } from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '../components/ui/sheet';
import {
  FilterSection,
  FilterState,
} from '../components/Projects/FilterSection';
import PaginationControls from '../components/PaginationControls';
import { Link } from '@remix-run/react';
import axios from 'axios';
import type { Project } from '../lib/Types';
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator, BreadcrumbPage } from '../components/ui/breadcrumb';

const ProjectCard = ({
  username,
  price,
  area,
  title,
  location,
  project,
  images,
  isVerified,
}: {
  username: string;
  price: string;
  area: string;
  title: string;
  location: string;
  project: string;
  images: string[];
  isVerified: boolean;
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false);

  // Create URL-friendly title
  const urlTitle = title.toLowerCase().replace(/\s+/g, '-');

  return (
    <Card className="h-full lg:h-60 mb-8 lg:mb-4 bg-white border-2 border-gray-200 overflow-hidden font-inter">
      <div className="relative h-full flex flex-col lg:flex-row items-center">
        <div className="w-full lg:w-1/4 h-40 lg:h-full relative flex-shrink-0">
          {isVerified && (
            <span className="absolute top-3 left-3 z-10 inline-flex items-center gap-1">
              <img src="/Verified.svg" alt="Verified" className="size-[70%]" />
            </span>
          )}

          <Carousel className="w-full h-full">
            <CarouselContent>
              {images.map((image, index) => (
                <CarouselItem key={index}>
                  <CarouselImage
                    src={image}
                    alt=""
                    className="h-40 lg:h-[240px]"
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious />
            <CarouselNext />
          </Carousel>
        </div>
        <div className="w-full h-full flex flex-col justify-center p-4 lg:w-3/4 relative">
          <div className="flex justify-between items-center mb-2 gap-x-5 overflow-x-auto md:overflow-x-visible scrollbar-hide">
            <div>
              <h3 className="text-lg font-semibold truncate mb-1">{title}</h3>
              <p className="text-sm text-gray-500 truncate">{location}</p>
            </div>
            <div className="flex gap-2">
              <button
                className="p-2 rounded-full bg-[#F1F1FF] hover:bg-[#E5E5FF] transition-colors"
                onClick={() => {
                  /* Share functionality */
                }}
              >
                <img src="/mdi-light_share.svg" />
              </button>
              <button
                className="p-2 rounded-full bg-[#F1F1FF] hover:bg-[#E5E5FF] transition-colors"
                onClick={() => setIsBookmarked(!isBookmarked)}
              >
                {/* Using Bookmark from Lucide instead of SVG */}
                <Bookmark
                  className={`h-5 w-5 ${isBookmarked ? 'fill-current' : ''}`}
                />
              </button>
            </div>
          </div>

          <div className="w-full pt-6 flex flex-col lg:flex-row justify-start gap-y-4 lg:gap-y-0 mb-6">
            <div className="flex gap-x-6 xl:gap-x-12 text-nowrap overflow-x-scroll scrollbar-hide">
              <div className="pr-8 xl:pr-12 border-r-2">
                <h3 className="text-sm text-gray-500">Budget Estimate</h3>
                <h1 className="text-medium font-medium mt-1">{price}</h1>
              </div>
              <div className="pr-8 xl:pr-12 border-r-2">
                <h3 className="text-sm text-gray-500">Land Area</h3>
                <h1 className="text-medium font-medium mt-1">{area} sq ft</h1>
              </div>
              <div>
                <h3 className="text-sm text-gray-500">Project type</h3>
                <div className="flex items-center gap-x-2">
                  <h1 className="text-medium font-medium mt-1">{project}</h1>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-gray-400 mt-1" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Type of development project</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4 gap-x-4">
            <p className="hidden sm:block text-sm text-gray-500">
              Posted by : {username}
            </p>
            <div className="flex gap-2">
              <Button variant="outline" className="bg-white border-orospaces-purple hover:bg-orospaces-purple/10 text-orospaces-purple hover:text-orospaces-purple"><Link to={`/projects/${urlTitle}`}>View Details</Link></Button>
              <Button className="bg-orospaces-purple hover:bg-orospaces-purple/90 text-white" asChild>
                Get Owner Contact
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

const ProjectList = ({ projects }: { projects: any[] }) => {
  return (
    <div className="flex-auto">
      {projects.length > 0 ? (
        projects.map((project) => (
          <div
            key={project.id}
            className="relative flex gap-2 flex-col lg:flex-row lg:items-center"
          >
            <div className="w-full gap-y-3 flex-auto">
              <ProjectCard {...project} />
            </div>
          </div>
        ))
      ) : (
        <p className="text-gray-500">No projects found.</p>
      )}
    </div>
  );
};

export async function loader({ request }: LoaderFunctionArgs) {
  /** getting query params for filtering the projects */
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1');
  // const limit = 1;
  const landTypes = url.searchParams.get('landTypes')?.split(',') || [];
  const projectTypes = url.searchParams.get('projectTypes')?.split(',') || [];
  // const query = url.searchParams.get('query');
  const landAreaMin = parseInt(url.searchParams.get('landAreaMin') || '0');
  const landAreaMax = parseInt(url.searchParams.get('landAreaMax') || '100000');
  const budgetMin = parseInt(url.searchParams.get('budgetMin') || '0');
  const budgetMax = parseInt(url.searchParams.get('budgetMax') || '1000000');
  const isVerified = url.searchParams.get('isVerified');

  const params = {
    landTypes: Array.isArray(landTypes) ? landTypes.join(',') : '',
    projectTypes: Array.isArray(projectTypes) ? projectTypes.join(',') : '',
    // query,  //todo: implement this search, address, city. not working.
    landAreaMin,
    landAreaMax,
    budgetMin,
    budgetMax,
    isVerified,
    page,
  };

  /** fetching the projects */
  try {
    const response = await axios.get('http://localhost:4000/projects', {
      params,
    });
    const projectsArray: Project[] = response.data.data;
    const filteredProjects = projectsArray.map((project) => {
      return {
        ...project,
        id: project.slug,
        username: 'Not given by BE',
        phone: 'Not given by BE',
        email: 'Not given by BE',
        price: project.price.toString(),
        area: project.area.toString(),
        location: `${project.address}, ${project.city}, ${project.pincode}`,
        mapCoordinates: project.location,
        project: project.projectType,
        views: project.views || 0,
        inquiries: project.inquiries || 0,
        status: project.projectStatus,
        isVerified: project.projectVerification === 'VARIFIED' ? true : false,
        postedDate: new Date(project.postedDate).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'long',
          year: 'numeric',
        }),
      };
    });

    return Response.json({
      projects: filteredProjects,
      totalProjects: response.data.pagination.total,
      page,
      limit: response.data.pagination.limit,
      totalPages: response.data.pagination.totalPages,
    });
  } catch (error) {
    console.error('Error fetching projects:\n', error);
    return [];
  }
}

export default function Projects() {
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    projects,
    totalProjects,
    page: initialPage,
    totalPages,
  } = useLoaderData<{
    projects: Project[];
    totalProjects: number;
    page: number;
    itemsPerPage: number;
    totalPages: number;
    limit: number;
  }>();

  const handleFilterChange = (filters: FilterState) => {
    const newParams = new URLSearchParams(searchParams);

    // Update URL params
    if (filters.location) {
      newParams.set('location', filters.location);
    } else {
      newParams.delete('location');
    }

    if (filters.landTypes.length > 0) {
      newParams.set('landTypes', filters.landTypes.join(','));
    } else {
      newParams.delete('landTypes');
    }

    if (filters.projectTypes.length > 0) {
      newParams.set('projectTypes', filters.projectTypes.join(','));
    } else {
      newParams.delete('projectTypes');
    }

    newParams.set('landAreaMin', filters.landArea[0].toString());
    newParams.set('landAreaMax', filters.landArea[1].toString());
    newParams.set('budgetMin', filters.budget[0].toString());
    newParams.set('budgetMax', filters.budget[1].toString());

    if (filters.isVerified) {
      newParams.set('isVerified', 'true');
    } else {
      newParams.delete('isVerified');
    }

    // Reset to page 1 when filters change
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  const handleFilterReset = () => {
    setSearchParams(new URLSearchParams());
  };

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', newPage.toString());
    setSearchParams(newParams);
  };

  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <div className="flex-grow p-4 lg:p-8">
        {/* Add Breadcrumb */}
        <Breadcrumb className="mb-8">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Projects</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        

          {/* Mobile Filter Button */}
          <div className="lg:hidden mb-8">
            <Sheet>
              <SheetTrigger asChild>
                <button className="p-2 hover:bg-gray-100 rounded-lg">
                  <Menu className="h-6 w-6" />
                </button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                <FilterSection
                  onFilterChange={handleFilterChange}
                  onReset={handleFilterReset}
                />
              </SheetContent>
            </Sheet>
          </div>

        <div className="flex flex-col lg:flex-row gap-x-10">
          {/* Desktop Filter Section */}
          <div className="hidden lg:block">
            <FilterSection
              onFilterChange={handleFilterChange}
              onReset={handleFilterReset}
            />
          </div>

          {/* Project List Section */}
          <div className="lg:w-2/3 flex-grow">
          <h1 className="text-2xl font-bold mb-5">{projects.length} results | Projects in {projects[0].city || 'N/A'}</h1>
            <ProjectList projects={projects} />
            <PaginationControls
              page={initialPage}
              totalPages={totalPages}
              totalRecords={totalProjects}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
