import React, { useState, useMemo, useCallback } from 'react';
import Navbar from "../components/Navbar";
import { But<PERSON> } from "@nextui-org/react";
import PaginationControls from "../components/PaginationControls";
import { Card } from "../components/ui/card";
import { Sidebar } from "./profile";

// Mock data
const mockProjects = [
    {
      id: '75458283',
      username: 'Orospaces User',
      phone: '+91 6006925352',
      email: '<EMAIL>',
      postedDate: '2024-09-12',
      price: '₹ 1.25 Cr - ₹ 1.5 Cr',
      area: '1365 sqft',
      title: 'Agricultural Land',
      location: '506 Division Road, Wadala Truck Terminal, Mumbai',
      images: ['https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'],
      views: 120,
      inquiries: 9,
      status: 'all Enquiries'
    },
    {
          id: '75458284',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-13',
          price: '₹ 2.0 Cr - ₹ 2.5 Cr',
          area: '1500 sqft',
          title: 'Commercial Space',
          location: '123 Business Park, Andheri East, Mumbai',
          images: ['https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 85,
          inquiries: 6,
          status: 'all Enquiries'
        },
        {
          id: '75458285',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-14',
          price: '₹ 75 Lakh - ₹ 90 Lakh',
          area: '900 sqft',
          title: 'Residential Apartment',
          location: '789 Harmony Heights, Powai, Mumbai',
          images: ['https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 150,
          inquiries: 12,
          status: 'all Enquiries'
        },
        {
          id: '75458286',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-15',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '2000 sqft',
          title: 'Luxury Villa',
          location: '456 Seaside Avenue, Juhu, Mumbai',
          images: ['https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 200,
          inquiries: 15,
          status: 'all Enquiries'
        },
        {
          id: '75458287',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-16',
          price: '₹ 50 Lakh - ₹ 60 Lakh',
          area: '600 sqft',
          title: 'Studio Apartment',
          location: '321 Metro View, Ghatkopar, Mumbai',
          images: ['https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 80,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458288',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-17',
          price: '₹ 1.8 Cr - ₹ 2.2 Cr',
          area: '1200 sqft',
          title: 'Office Space',
          location: '987 Tech Park, Goregaon, Mumbai',
          images: ['https://images.unsplash.com/photo-1497366672149-e5e4b4d34eb3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366672149-e5e4b4d34eb3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366672149-e5e4b4d34eb3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 110,
          inquiries: 8,
          status: 'all Enquiries'
        },
        {
          id: '75458289',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-18',
          price: '₹ 2.5 Cr - ₹ 3.0 Cr',
          area: '1800 sqft',
          title: 'Penthouse',
          location: '654 Skyline Towers, Worli, Mumbai',
          images: ['https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 180,
          inquiries: 14,
          status: 'all Enquiries'
        },
        {
          id: '75458290',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-19',
          price: '₹ 1.0 Cr - ₹ 1.2 Cr',
          area: '1000 sqft',
          title: 'Shop Space',
          location: '210 Market Street, Bandra, Mumbai',
          images: ['https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 95,
          inquiries: 5,
          status: 'all Enquiries'
        },
        {
          id: '75458291',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-20',
          price: '₹ 80 Lakh - ₹ 95 Lakh',
          area: '950 sqft',
          title: 'Office Space',
          location: '456 Business Park, Bandra West, Mumbai',
          images: ['https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 95,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458292',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-21',
          price: '₹ 1.8 Cr - ₹ 2.2 Cr',
          area: '1800 sqft',
          title: 'Penthouse',
          location: '789 Skyview Towers, Worli, Mumbai',
          images: ['https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 180,
          inquiries: 15,
          status: 'all Enquiries'
        },
        {
          id: '75458293',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-22',
          price: '₹ 70 Lakh - ₹ 85 Lakh',
          area: '850 sqft',
          title: 'Studio Apartment',
          location: '101 Artist Colony, Juhu, Mumbai',
          images: ['https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 110,
          inquiries: 8,
          status: 'all Enquiries'
        },
        {
          id: '75458294',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-23',
          price: '₹ 2.5 Cr - ₹ 3.0 Cr',
          area: '2200 sqft',
          title: 'Beachfront Villa',
          location: '555 Coastal Road, Versova, Mumbai',
          images: ['https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 200,
          inquiries: 18,
          status: 'all Enquiries'
        },
        {
          id: '75458295',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-24',
          price: '₹ 1.1 Cr - ₹ 1.3 Cr',
          area: '1100 sqft',
          title: 'Duplex Apartment',
          location: '222 Green Valley, Goregaon, Mumbai',
          images: ['https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 130,
          inquiries: 11,
          status: 'all Enquiries'
        },
        {
          id: '75458296',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-25',
          price: '₹ 60 Lakh - ₹ 75 Lakh',
          area: '750 sqft',
          title: '1 BHK Apartment',
          location: '333 Sunshine Complex, Malad, Mumbai',
          images: ['https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 90,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458297',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-26',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          images: ['https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'

        },
        {
          id: '75458298',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-27',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          images: ['https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
        {
          id: '75458299',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-28',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          images: ['https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
        {
          id: '75458300',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-29',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          images: ['https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80'],
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
  ];

  // Memoized ProjectCard Component
  const ProjectCard = React.memo(({
    price,
    area,
    title,
    location,
    images,
    status,
    onApprove,
  }: {
    price: string;
    area: string;
    title: string;
    location: string;
    images: string[];
    status: string;
    onApprove: () => void;

  }) => (
    <Card className=" h-80 relative overflow-clip border-2 border-gray-200">
      <div className=" flex flex-col">
        <div className="w-full">
          <div className="relative w-full">
            <img src={images[0]} alt={title} className="w-full h-40 object-cover" />
            <div className="absolute top-3 left-3 bg-black/60 text-white px-2 py-1 rounded text-xs">
              Last Edited: 15th May, 2024
            </div>
          </div>
        </div>
          <div className="h-40 flex flex-col items-start justify-between p-3 flex-shrink-0">
            <h3 className="text-lg font-semibold ">{title}</h3>
            <p className="w-full truncate text-sm text-gray-500 mb-1 -mt-1">{location}</p>
            <div className="text-sm text-gray-600 font-bold">{price} | {area}</div>
            <div className="space-2 p-2 w-full">
            <Button onPress={onApprove} disabled={status === 'approved'} className="w-full border-2 font-medium border-[#7065F0] text-[#7065F0] rounded-lg h-8 p-3">Continue Posting</Button>
          </div>
          </div> 
        </div>
    </Card>
  ));

  ProjectCard.displayName = 'ProjectCard';

  // Memoized ProjectList Component
  const ProjectList = React.memo(({
    projects,
    searchQuery,
    setSearchQuery,
  }: {
    projects: typeof mockProjects;
    searchQuery: string;
    setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  }) => {
    return (
      <>
      <div className="mb-5">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by title, location or price"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full h-11 px-4 py-2 border-1 border-gray-200 rounded-lg focus:outline-none focus:border-[#7065F0]"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              )}
            </div>
        </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 w-full relative">
        {projects.length > 0 ? (
          projects.map((project) => (
            <div key={project.id} className="">
              <div className="">
                <ProjectCard  
                  {...project}
                  onApprove={() => {}}
                />
              </div>
            </div>
          ))
        ) : (
          <p className=" text-gray-500">No projects found.</p>
        )}
      </div>
      </>
    );
  });

  ProjectList.displayName = 'ProjectList';

  export default function Drafts() {
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(8);
    const [projects, setProjects] = useState(mockProjects);

    const [searchQuery, setSearchQuery] = useState('');

    const filteredProjects = useMemo(() => {
      let filtered = [...projects];

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter((project) => 
          project.price.toLowerCase().includes(searchQuery.toLowerCase()) ||
          project.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          project.title.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      return filtered;
    }, [projects, searchQuery]);

    const totalRecords = filteredProjects.length;
    const totalPages = Math.ceil(totalRecords / itemsPerPage);

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalRecords);
    const currentProjects = filteredProjects.slice(startIndex, endIndex);

    const handlePageChange = useCallback((newPage: number) => {
      setPage(newPage);
    }, []);

    const handleRowsPerPageChange = useCallback((rows: number) => {
      setItemsPerPage(rows);
      setPage(1); 
    }, []);

    return (
      <div className="flex min-h-screen flex-col bg-gray-50">
        <Navbar />
        <div className="flex flex-grow relative">
          <Sidebar />
          <div className="flex-grow p-4 lg:p-8 overflow-x-hidden overflow-y-auto h-[calc(90vh)]">
            <div className="md:flex md:justify-between md:items-center mb-8">
              <h1 className="text-2xl font-bold mb-4 md:mb-0">Project Drafts</h1>
            </div>

            <div className="rounded-lg">
              <ProjectList
                projects={currentProjects}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
              />

              <PaginationControls
                page={page}
                totalPages={totalPages}
                startIndex={startIndex}
                endIndex={endIndex}
                totalRecords={totalRecords}
                onPageChange={handlePageChange}
                rowsPerPage={itemsPerPage}
                onRowsPerPageChange={handleRowsPerPageChange}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }