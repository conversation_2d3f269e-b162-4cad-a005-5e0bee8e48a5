{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "out-tsc/admin", "lib": ["DOM", "DOM.Iterable", "ES2019"], "types": ["@remix-run/node", "vite/client"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "target": "ES2022", "strict": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": "."}, "include": ["app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx"], "exclude": ["dist", "tests/**/*.spec.ts", "tests/**/*.test.ts", "tests/**/*.spec.tsx", "tests/**/*.test.tsx", "tests/**/*.spec.js", "tests/**/*.test.js", "tests/**/*.spec.jsx", "tests/**/*.test.jsx", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}