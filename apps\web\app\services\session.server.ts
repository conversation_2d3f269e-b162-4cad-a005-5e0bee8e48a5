import { createCookieSessionStorage, redirect } from "@remix-run/node";

// IMPORTANT: Add SESSION_SECRET to your .env file in the root of your Remix project.
// e.g., SESSION_SECRET="your-long-random-secure-string"
const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  // In production, you'll want to ensure this is set.
  // For local development, you can use a default, but it's better practice to use .env
  if (process.env.NODE_ENV === "production") {
    throw new Error("SESSION_SECRET must be set in your environment variables.");
  }
  console.warn("SESSION_SECRET is not set. Using a default for development. Please set it in .env for production.");
}

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session", // Name of the cookie set on the browser
    httpOnly: true,
    path: "/",
    sameSite: "lax",
    secrets: [sessionSecret || "DEV_FALLBACK_SECRET"], // Fallback for dev if not in .env
    secure: process.env.NODE_ENV === "production", // True if HTTPS is used
    maxAge: 60 * 60 * 24 * 30, // Expires in 30 days
  },
});

export async function createUserSession(apiToken: string, redirectTo: string) {
  const session = await sessionStorage.getSession();
  session.set("apiToken", apiToken); // Store the token from your backend API
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await sessionStorage.commitSession(session),
    },
  });
}

export async function getUserSession(request: Request) {
  return sessionStorage.getSession(request.headers.get("Cookie"));
}

export async function getApiToken(request: Request): Promise<string | null> {
  const session = await getUserSession(request);
  const apiToken = session.get("apiToken");
  if (!apiToken || typeof apiToken !== "string") return null;
  return apiToken;
}

export async function destroyUserSession(request: Request, redirectTo: string) {
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await sessionStorage.destroySession(session),
    },
  });
}