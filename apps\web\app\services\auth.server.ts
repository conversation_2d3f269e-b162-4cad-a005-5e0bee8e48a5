import axios from "axios";

const API_URL = "http://localhost:4000";

export type User = {
  id?: string;
  email: string;
  name: string;
  phone: string;
  userType: "owner" | "builder";
  phoneIsVerified?: boolean;
  emailIsVerified?: boolean;
  profileImage?: string | null;
  subscription?: {
    type: string;
    maxContactRequests: number;
    maxProjects: number;
    planId: string;
    status: string;
    subscriptionId: string;
  } | null;
};

export async function signup(userData: {
  name: string;
  email: string;
  password: string;
  phone: string;
  userType: "owner" | "builder";
}) {
  try {
    await axios.post(`${API_URL}/auth/signup`, userData, {
      withCredentials: true
    });
    
    return { 
      success: true, 
      user: {
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        userType: userData.userType
      }
    };
  } catch (error: any) {
    if (error.response?.data?.message?.includes('users_phone_unique') || 
        error.message?.includes('users_phone_unique')) {
      throw new Error("This phone number is already registered. Please use a different phone number or try to login.");
    }
    console.error("Signup error:", error.response?.data || error.message);
    throw new Error(error.response?.data?.message || "Registration failed");
  }
}

export async function verifyOTP(phone: string, totp: string): Promise<{ success: boolean; token?: string; error?: string }> {
  try {
    // Assuming your backend API returns a JSON like { "token": "your_jwt_string" }
    const response = await axios.post<{ token: string }>(
      `${API_URL}/auth/verify/phone`,
      { phone, totp },
      // withCredentials: true is not strictly necessary here for the Remix server's call to the API,
      // as the Remix server isn't relying on browser cookies for this specific server-to-server call.
      // However, it doesn't hurt. The crucial part is handling the response correctly.
      { validateStatus: status => status >= 200 && status < 500 } // Handle API errors gracefully
    );

    if (response.status === 200 && response.data && response.data.token) {
      return {
        success: true,
        token: response.data.token,
      };
    } else {
      // Capture error message from API if available, otherwise provide a generic one
      const errorMessage = response.data && (response.data as any).message 
                           ? (response.data as any).message
                           : `OTP verification failed with status: ${response.status}`;
      console.error("OTP verification error from API:", response.data);
      return { success: false, error: errorMessage };
    }
  } catch (error: any) {
    console.error("Axios OTP verification error:", error.message);
    // Check if it's an axios error with a response
    if (axios.isAxiosError(error) && error.response) {
        const errorMessage = error.response.data?.message || "OTP verification failed due to network or server error.";
        return { success: false, error: errorMessage };
    }
    return { success: false, error: "OTP verification failed" };
  }
}

export async function resendOTP(phone: string) {
  try {
    await axios.post(
      `${API_URL}/auth/send/totp`,
      { type: "phone", sendVia: phone },
      { withCredentials: true }
    );
    return { success: true };
  } catch (error: any) {
    console.error("Resend OTP error:", error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.message || "Could not send OTP"
    };
  }
}

// Login function
export async function login(email: string, password: string) {
  try {
    const response = await axios.post(
      `${API_URL}/auth/signin`,
      { email, password },
      { withCredentials: true }
    );
    
    return { 
      success: true,
      token: response.data?.token || null
    };
  } catch (error: any) {
    console.error("Login error:", error.response?.data || error.message);
    throw new Error(error.response?.data?.message || "Invalid login credentials");
  }
}

// Logout function
export async function logout() {
  try {
    await axios.get(`${API_URL}/auth/signout`, { withCredentials: true });
    return { success: true };
  } catch (error: any) {
    console.error("Logout error:", error.response?.data || error.message);
    return { success: false };
  }
}
