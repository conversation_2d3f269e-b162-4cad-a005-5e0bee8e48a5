import { type ReactNode } from "react";
import Navbar from "./Navbar";
import { Sidebar } from "~/routes/profile";

interface DashboardLayoutProps {
  children: ReactNode;
  title: string;
}

export default function DashboardLayout({ children, title }: DashboardLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <Navbar />
      <div className="flex flex-grow">
        <Sidebar />
        <div className="flex-grow p-4 md:p-8 overflow-x-hidden overflow-y-auto h-[calc(90vh)]">
          <div className="md:flex md:justify-between md:items-center mb-8">
            <h1 className="text-2xl font-bold mb-4 md:mb-0">{title}</h1>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}