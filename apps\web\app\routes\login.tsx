import { useState } from "react";
import { redirect } from "@remix-run/node";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useNavigation, Link } from "@remix-run/react";
import { login } from "../services/auth.server";
import { createUserSession } from "../services/session.server";
import { getCurrentUser } from "../utils/auth.server";
import { cn } from "../lib/utils";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Loader2 } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselDots,
  CarouselImage,
} from "../components/ui/carousel";

type ActionData = { error?: string } | undefined;

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getCurrentUser(request);
  if (user) {
    return redirect("/dashboard");
  }
  return {};
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return { error: "Email and password are required." } as const;
  }

  try {
    const result = await login(email, password);

    if (result.success && result.token) {
      return await createUserSession(result.token, "/dashboard");
    } else {
      return { error: "Invalid email or password." } as const;
    }
  } catch (error: any) {
    console.error("Login action error:", error);
    return { 
      error: error.message || "An unexpected error occurred during login." 
    } as const;
  }
}

export default function LoginPage({ className, ...props }: React.ComponentProps<"div">) {
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isLoading = navigation.state === "submitting";
  const images = ["/Image_1.svg", "/Image_2.svg", "/Image_3.svg"];

  return (
    <div className={cn("w-full h-screen flex justify-between items-center gap-6 p-6 md:p-8", className)} {...props}>
      <div className="hidden md:flex w-1/2 h-full items-center">
        <Carousel className="w-full max-h-[95vh]">
          <CarouselContent>
            {images.map((image, index) => (
              <CarouselItem key={index}>
                <CarouselImage src={image} alt="" className="w-full max-h-[95vh] object-contain" />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselDots 
            count={3} 
            className="bottom-6 left-1/2 -translate-x-1/2 top-auto"
          />
        </Carousel>
      </div>
      
      <div className="w-full md:w-1/2 h-full flex items-center justify-center font-poppins">
        <Form method="post" className="w-full max-w-md space-y-6">
          <div className="flex flex-col items-center text-center">
            <h1 className="text-2xl font-semibold mb-6">Login to your Orospaces Account</h1>
          </div>

          {actionData?.error && (
            <div className="p-3 text-sm text-red-500 bg-red-50 rounded-md">
              {actionData.error}
            </div>
          )}
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                autoComplete="email"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  to="/forgot-password"
                  className="ml-auto text-sm text-gray-600 hover:text-gray-900"
                >
                  Forgot password?
                </Link>
              </div>
              <Input
                id="password"
                name="password"
                type="password"
                required
                autoComplete="current-password"
              />
            </div>
          </div>

          <Button
            type="submit"
            className="w-full bg-orospaces-purple hover:bg-orospaces-purple/90 text-white"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Logging in...
              </>
            ) : (
              "Login to your Account"
            )}
          </Button>

          <div className="text-center text-sm">
            Don't have an account?{" "}
            <Link
              to="/signup"
              className="text-orospaces-purple underline hover:text-orospaces-purple/90"
            >
              Register Now
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
}

