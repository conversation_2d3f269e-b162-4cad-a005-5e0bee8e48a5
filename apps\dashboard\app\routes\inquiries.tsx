import React, { useState, useMemo, useCallback } from 'react';
import Navbar from "../components/Navbar";
import PaginationControls from "../components/PaginationControls";
import { Card } from "../components/ui/card";
import { DatePickerWithRange } from "../components/DatePickerWithRange";
import { endOfDay } from 'date-fns';
import { Sidebar } from "./profile";
import { Button } from '../components/ui/button';
import { Button as NextUIButton } from '@nextui-org/react';

// Mock data
const mockProjects = [
    {
      id: '75458283',
      username: 'Orospaces User',
      phone: '+91 6006925352',
      email: '<EMAIL>',
      postedDate: '2024-09-12',
      price: '₹ 1.25 Cr - ₹ 1.5 Cr',
      area: '1365 sqft',
      title: 'Agricultural Land',
      location: '506 Division Road, Wadala Truck Terminal, Mumbai',
      image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
      views: 120,
      inquiries: 9,
      status: 'all Enquiries'
    },
    {
          id: '75458284',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-13',
          price: '₹ 2.0 Cr - ₹ 2.5 Cr',
          area: '1500 sqft',
          title: 'Commercial Space',
          location: '123 Business Park, Andheri East, Mumbai',
          image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 85,
          inquiries: 6,
          status: 'all Enquiries'
        },
        {
          id: '75458285',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-14',
          price: '₹ 75 Lakh - ₹ 90 Lakh',
          area: '900 sqft',
          title: 'Residential Apartment',
          location: '789 Harmony Heights, Powai, Mumbai',
          image: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 150,
          inquiries: 12,
          status: 'all Enquiries'
        },
        {
          id: '75458286',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-15',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '2000 sqft',
          title: 'Luxury Villa',
          location: '456 Seaside Avenue, Juhu, Mumbai',
          image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 200,
          inquiries: 15,
          status: 'all Enquiries'
        },
        {
          id: '75458287',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-16',
          price: '₹ 50 Lakh - ₹ 60 Lakh',
          area: '600 sqft',
          title: 'Studio Apartment',
          location: '321 Metro View, Ghatkopar, Mumbai',
          image: 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 80,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458288',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-17',
          price: '₹ 1.8 Cr - ₹ 2.2 Cr',
          area: '1200 sqft',
          title: 'Office Space',
          location: '987 Tech Park, Goregaon, Mumbai',
          image: 'https://images.unsplash.com/photo-1497366672149-e5e4b4d34eb3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 110,
          inquiries: 8,
          status: 'all Enquiries'
        },
        {
          id: '75458289',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-18',
          price: '₹ 2.5 Cr - ₹ 3.0 Cr',
          area: '1800 sqft',
          title: 'Penthouse',
          location: '654 Skyline Towers, Worli, Mumbai',
          image: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 180,
          inquiries: 14,
          status: 'all Enquiries'
        },
        {
          id: '75458290',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-19',
          price: '₹ 1.0 Cr - ₹ 1.2 Cr',
          area: '1000 sqft',
          title: 'Shop Space',
          location: '210 Market Street, Bandra, Mumbai',
          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 95,
          inquiries: 5,
          status: 'all Enquiries'
        },
        {
          id: '75458291',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-20',
          price: '₹ 80 Lakh - ₹ 95 Lakh',
          area: '950 sqft',
          title: 'Office Space',
          location: '456 Business Park, Bandra West, Mumbai',
          image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 95,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458292',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-21',
          price: '₹ 1.8 Cr - ₹ 2.2 Cr',
          area: '1800 sqft',
          title: 'Penthouse',
          location: '789 Skyview Towers, Worli, Mumbai',
          image: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 180,
          inquiries: 15,
          status: 'all Enquiries'
        },
        {
          id: '75458293',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-22',
          price: '₹ 70 Lakh - ₹ 85 Lakh',
          area: '850 sqft',
          title: 'Studio Apartment',
          location: '101 Artist Colony, Juhu, Mumbai',
          image: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 110,
          inquiries: 8,
          status: 'all Enquiries'
        },
        {
          id: '75458294',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-23',
          price: '₹ 2.5 Cr - ₹ 3.0 Cr',
          area: '2200 sqft',
          title: 'Beachfront Villa',
          location: '555 Coastal Road, Versova, Mumbai',
          image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 200,
          inquiries: 18,
          status: 'all Enquiries'
        },
        {
          id: '75458295',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-24',
          price: '₹ 1.1 Cr - ₹ 1.3 Cr',
          area: '1100 sqft',
          title: 'Duplex Apartment',
          location: '222 Green Valley, Goregaon, Mumbai',
          image: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 130,
          inquiries: 11,
          status: 'all Enquiries'
        },
        {
          id: '75458296',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-25',
          price: '₹ 60 Lakh - ₹ 75 Lakh',
          area: '750 sqft',
          title: '1 BHK Apartment',
          location: '333 Sunshine Complex, Malad, Mumbai',
          image: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 90,
          inquiries: 7,
          status: 'all Enquiries'
        },
        {
          id: '75458297',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-26',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'

        },
        {
          id: '75458298',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-27',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
        {
          id: '75458299',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: 'siddharthmotwani0*@gmail.com',
          postedDate: '2024-09-28',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
        {
          id: '75458300',
          username: 'Orospaces User',
          phone: '+91 6006925352',
          email: '<EMAIL>',
          postedDate: '2024-09-29',
          price: '₹ 3.5 Cr - ₹ 4.0 Cr',
          area: '3000 sqft',
          title: 'Luxury Penthouse',
          location: '999 Skyscraper Avenue, Prabhadevi, Mumbai',
          image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=1024&q=80',
          views: 250,
          inquiries: 22,
          status: 'all Enquiries'
        },
  ];

  // Memoized ProjectCard Component
  const ProjectCard = React.memo(({
    username,
    phone,
    email,
    postedDate,
    price,
    area,
    title,
    location,
    image,
    status,
    onApprove,
    onDeny
  }: {
    username: string;
    phone: string;
    email: string;
    postedDate: string;
    price: string;
    area: string;
    title: string;
    location: string;
    image: string;
    status: string;
    onApprove: () => void;
    onDeny: () => void;
  }) => (
    <Card className="py-1 w-full mb-5 border-2 border-gray-200">
      <div className="flex flex-col">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center w-full p-3 space-y-2 lg:space-y-0">
          <h1 className="font-semibold text-lg">{username}</h1>
          <div className="flex flex-col lg:flex-row gap-x-8 space-y-1 lg:space-y-0">
            <div className="flex">
              <span className="text-sm font-medium text-gray-500">{phone}</span>
            </div>
            <div className="flex">
              <span className="text-sm font-medium text-gray-500">{email}</span>
            </div>
          </div> 
        </div>
        <div className="flex flex-col lg:flex-row gap-4 items-center shadow-none p-3 bg-[#F1F1FF]">
          <div className="w-full lg:w-24 h-24 relative flex-shrink-0">
            <img 
              src={image} 
              alt={title}
              width={384}
              height={384}
              className="rounded-lg object-cover w-full h-full"
            />
          </div>
          <div className="w-full truncate text-wrap relative space-y-1 flex-shrink-0">
            <div className="text-sm text-gray-600 font-medium">{price} | {area}</div>
            <h3 className="text-lg font-semibold truncate">{title}</h3>
            <p className="text-sm text-gray-500 truncate">{location}</p>
          </div> 
        </div>
        <div className="flex flex-col lg:flex-row gap-2 justify-center lg:items-center lg:justify-between p-4">
          <p className="text-sm font-medium text-gray-500">Received on : {new Date(postedDate).toLocaleDateString()}</p>
          <div className="sm:space-x-3 space-y-2 flex flex-col sm:flex-row sm:items-end">
            <NextUIButton onPress={onApprove} disabled={status === 'declined'} className="border-2 font-medium border-[#7065F0] text-[#7065F0] rounded-lg h-8 p-3">Deny Inquiry</NextUIButton>
            <NextUIButton onPress={onDeny} disabled={status === 'approved'} className="border-2 font-medium border-none bg-[#7065F0] text-white rounded-lg h-8 p-3">Approve Inquiry</NextUIButton>
          </div>
        </div>
      </div>
    </Card>
  ));

  ProjectCard.displayName = 'ProjectCard';

  // Memoized ProjectList Component
  const ProjectList = React.memo(({
    projects,
    searchQuery,
    setSearchQuery,
    handleDateChange,
    dateRange,
    updateProjectStatus
  }: {
    projects: typeof mockProjects;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    handleDateChange: (range: { from: Date | null; to: Date | null }) => void;
    dateRange: { from: Date | null; to: Date | null };
    updateProjectStatus: (id: string, newStatus: string) => void;
  }) => {
    const [inputValue, setInputValue] = useState(searchQuery);
    
    const handleSearch = () => {
      setSearchQuery(inputValue);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleSearch();
      }
    };

    return (
      <div className="flex-auto bg-white p-3 rounded-md">
        <div className="mb-4 flex flex-col lg:flex-row justify-start items-end gap-y-4 lg:gap-x-16">
          <div className="flex-1 w-full lg:max-w-lg space-y-2">
            <h6 className='text-sm text-gray-500'>Search Inquiries</h6>
            <div className="w-full flex gap-2">
              <div className="w-full relative">
              <input
                type="text"
                placeholder="Search by phone or email..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full h-10 px-4 py-2 border-1 border-gray-300 rounded-md focus:outline-none"
              />
              {inputValue && (
                <button
                  onClick={() => setInputValue('')}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              )}
              </div>
              <Button
                onClick={handleSearch}
                variant="outline"
              >
                Search
              </Button>
            </div>
          </div>
          <DatePickerWithRange className="w-full lg:w-[300px]" onDateRangeChange={handleDateChange} initialDateRange={dateRange} />
        </div>
        {projects.length > 0 ? (
          projects.map((project) => (
            <div key={project.id} className="relative flex gap-2 flex-col lg:flex-row lg:items-center">
              <div className="w-full">
                <ProjectCard  
                  {...project}
                  onApprove={() => updateProjectStatus(project.id, 'approved')}
                  onDeny={() => updateProjectStatus(project.id, 'declined')}
                />
              </div>
            </div>
          ))
        ) : (
          <p className=" text-gray-500">No projects found.</p>
        )}
      </div>
    );
  });

  ProjectList.displayName = 'ProjectList';

  export default function Inquiries() {
    const [page, setPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(8);
    const [projects, setProjects] = useState(mockProjects);
    const [dateRange, setDateRange] = useState({ from: null, to: null });
    const [searchQuery, setSearchQuery] = useState('');
    const [currentStatus, setCurrentStatus] = useState('all Enquiries');

    const handleDateChange = useCallback((range) => {
      setDateRange(range);
      setPage(1); // Reset to first page when date filter changes
    }, []);

    const updateProjectStatus = useCallback((id: string, newStatus: string) => {
      setProjects((prevProjects) =>
        prevProjects.map((project) =>
          project.id === id ? { ...project, status: newStatus } : project
        )
      );
    }, []);

    // Calculate filtered projects based on search, date range, and status
    const filteredProjects = useMemo(() => {
      let filtered = [...projects];

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter((project) => 
          project.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
          project.email.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply date filter if both dates are selected
      if (dateRange.from && dateRange.to) {
        filtered = filtered.filter((project) => {
          const postedDate = new Date(project.postedDate);
          const fromDate = new Date(dateRange.from!);
          return fromDate <= postedDate && postedDate <= endOfDay(new Date(dateRange.to!));
        });
      }

      // Apply status filter
      if (currentStatus !== 'all Enquiries') {
        filtered = filtered.filter(project => project.status === currentStatus);
      }

      return filtered;
    }, [projects, searchQuery, dateRange, currentStatus]);

    const totalRecords = filteredProjects.length;
    const totalPages = Math.ceil(totalRecords / itemsPerPage);

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalRecords);
    const currentProjects = filteredProjects.slice(startIndex, endIndex);

    const handlePageChange = useCallback((newPage: number) => {
      setPage(newPage);
    }, []);

    const handleRowsPerPageChange = useCallback((rows: number) => {
      setItemsPerPage(rows);
      setPage(1); 
    }, []);

    return (
      <div className="flex min-h-screen flex-col bg-gray-50">
        <Navbar />
        <div className="flex flex-grow relative">
          <Sidebar />
          <div className="flex-grow p-4 lg:p-8 overflow-x-hidden overflow-y-auto h-[calc(90vh)]">
            <div className="md:flex md:justify-between md:items-center mb-8">
              <h1 className="text-2xl font-bold mb-4 md:mb-0">Inquiries</h1>
            </div>

            <div className="rounded-lg flex mb-6">
              {['all Enquiries', 'approved', 'declined'].map(status => (
                <button
                  key={status}
                  onClick={() => setCurrentStatus(status)}
                  className={`sm:px-8 lg:py-2 p-2 bg-white border-2 border-[#CECAF3] ${status === 'all Enquiries' ? 'border-r-0 rounded-l-md' : ''} ${status === 'declined' ? 'border-l-0 rounded-r-md' : ''} ${
                    currentStatus === status ? 'bg-[#F1F1FF] text-[#7065F0]' : 'hover:bg-[#F1F1FF] hover:text-[#7065F0]'
                  }`}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </button>
              ))}
            </div>
            
            <div className="rounded-lg ">
              <ProjectList
                projects={currentProjects}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                handleDateChange={handleDateChange}
                dateRange={dateRange}
                updateProjectStatus={updateProjectStatus}
              />

              <PaginationControls
                page={page}
                totalPages={totalPages}
                startIndex={startIndex}
                endIndex={endIndex}
                totalRecords={totalRecords}
                onPageChange={handlePageChange}
                rowsPerPage={itemsPerPage}
                onRowsPerPageChange={handleRowsPerPageChange}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }