{"private": true, "name": "admin", "scripts": {"dev": "remix vite:dev", "build": "remix vite:build", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "type": "module", "dependencies": {"@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@remix-run/node": "^2.14.0", "@remix-run/react": "^2.14.0", "@remix-run/serve": "^2.14.0", "@tailwindcss/postcss": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^4.4.0", "lucide-react": "^0.468.0", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.1.0", "tailwindcss": "^3.4.16"}, "devDependencies": {"@remix-run/dev": "^2.14.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}, "engines": {"node": ">=20"}, "sideEffects": false, "nx": {"projectType": "application", "sourceRoot": "apps/admin"}}