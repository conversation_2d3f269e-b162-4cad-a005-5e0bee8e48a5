import {
  <PERSON><PERSON>,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  <PERSON>rollRestoration,
  useLoaderData,
} from '@remix-run/react';
import type { MetaFunction, LinksFunction, LoaderFunctionArgs } from '@remix-run/node';
import styles from "./tailwind.css?url";
import { UserProvider } from "./context/user-context";
import Navbar from './components/Home/Navbar';
import { getCurrentUser } from './utils/auth.server';

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: styles },
  { 
    rel: "preload", 
    href: "/Landing_Page_Banner.svg", 
    as: "image" 
  },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap" },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap" },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" },
  { rel: "stylesheet", href: "https://api.fontshare.com/v2/css?f[]=satoshi@400,500,700&display=swap" },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" },
];

export const meta: MetaFunction = () => [
  {
    title: 'Orospaces',
  },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getCurrentUser(request);
  return Response.json({ user });
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <Navbar />
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const { user } = useLoaderData<typeof loader>();

  return (
    <UserProvider initialUser={user}>
      <Outlet />
    </UserProvider>
  );
}