import { useState } from 'react';
import PaginationControls from "../components/PaginationControls";
import { MoreVertical } from 'lucide-react';
import { mockProjects } from "../data/mockProjects";
import { Suspense } from "react";

const MenuButton = ({ isOpen, onToggle }: { 
  isOpen: boolean; 
  onToggle: () => void;
}) => (
  <div className="relative">
    <button onClick={onToggle} className="p-2 hover:bg-gray-100">
      <MoreVertical size={20} />
    </button>
    {isOpen && (
      <div className="absolute right-0 mt-2 w-44 bg-white rounded-lg shadow-lg border border-gray-200 py-2 px-1 z-50
        animate-in fade-in slide-in-from-top-1 duration-200">
        <button className="w-full px-4 py-2 text-left hover:bg-neutral-100 rounded-md transition-colors"
          onClick={onToggle}>
          View Ad Details
        </button>
        <button className="w-full px-4 py-2 text-left text-red-600 hover:bg-neutral-100 rounded-md transition-colors"
          onClick={onToggle}>
          Delete
        </button>
      </div>
    )}
  </div>
);

const ProjectCard = ({
  id,
  postedDate,
  location,
  image,
  projectType = "Open",
  userName,
  userContact,
}: {
  id: string;
  postedDate: string;
  location: string;
  image: string;
  projectType?: string;
  userName: string;
  userContact: string;
}) => {
  return (
    <div className="w-full py-3">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <div className="w-full h-48 md:w-20 md:h-20 relative flex-shrink-0">
          <img 
            src={image} 
            alt={`Property ${id}`}
            className="rounded-md object-cover w-full h-full"
            loading="lazy"
          />
        </div>
        <div className="flex-grow grid grid-cols-1 md:grid-cols-5 gap-2 md:gap-4 items-center">
          <div className="md:col-span-full space-y-1">
            <div className="text-md text-start font-medium truncate">ID: {id}</div>
            <div className="text-sm text-start text-gray-500 line-clamp-2 w-[75%]">{location}</div>
          </div>
          <div className="md:hidden">
            <div className="px-3 py-1 bg-yellow-50 text-yellow-600 rounded-full text-xs inline-block">
              {projectType}
            </div>
          </div>
          <div className="md:hidden text-sm">{userName}</div>
          <div className="md:hidden text-sm">{userContact}</div>
          <div className="md:hidden text-sm">{postedDate}</div>
        </div>
      </div>
    </div>
  );
};

const ProjectList = ({ projects }: { projects: any[] }) => {
  const [isMenuOpen, setIsMenuOpen] = useState<string | null>(null);

  return (
    <div>
      <div className="mb-4 grid grid-cols-1 md:grid-cols-7 gap-3 border-b border-gray-300 pb-2 overflow-x-hidden">
        <div className="font-medium text-gray-500 col-span-2">
          <span>Ads</span>
        </div>
        <div className="hidden md:block truncate font-medium text-gray-500">Project Type</div>
        <div className="hidden md:block truncate font-medium text-gray-500">User Name</div>
        <div className="hidden md:block truncate font-medium text-gray-500">User Contact</div>
        <div className="hidden md:block truncate font-medium text-gray-500">Posted On</div>
        <div className="hidden md:flex font-medium text-gray-500 justify-between gap-x-5">
          <span className="truncate">Status</span>
          <span className="truncate">Actions</span>
        </div>
      </div> 
      {projects.length > 0 ? (
        projects.map((project) => (
          <div key={project.id} className="grid grid-cols-1 md:grid-cols-7 gap-3 items-center border-b border-gray-200">
            <div className="md:col-span-2">
              <ProjectCard 
                id={project.id}
                postedDate={project.postedDate}
                location={project.location}
                image={project.image}
                userName={project.userName}
                userContact={project.userContact}
              />
            </div>
            <div className="hidden md:block md:col-span-1">
              <div className="px-3 py-1 bg-yellow-50 text-yellow-600 rounded-full text-xs inline-block">
                Open
              </div>
            </div>
            <div className="hidden md:block truncate md:col-span-1">{project.userName}</div>
            <div className="hidden md:block truncate md:col-span-1">{project.userContact}</div>
            <div className="hidden md:block truncate md:col-span-1">{project.postedDate}</div>
            <div className="hidden md:flex md:col-span-1 justify-between items-center gap-x-3">
              <StatusBadge status="active" />
              <MenuButton 
                isOpen={isMenuOpen === project.id}
                onToggle={() => setIsMenuOpen(isMenuOpen === project.id ? null : project.id)}
              />
            </div>
          </div>
        ))
      ) : (
        <p className="text-gray-500">No projects found.</p>
      )}
    </div>
  );
};

function LoadingProjectsSkeleton() {
  return (
    <div className="animate-pulse space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-24 bg-gray-100 rounded-md border border-gray-200" />
      ))}
    </div>
  );
}

const StatusBadge = ({ status }: { status: string }) => {
  // Only implementing active status for now as requested
  return (
    <div className="flex items-center justify-center truncate gap-2 rounded-sm py-1 px-2 bg-green-100 text-green-500">
      <div className="text-sm font-semibold truncate">
        Active
      </div>
    </div>
  );
};

export default function Projects() {
  const [page, setPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(8);
  const [currentStatus, setCurrentStatus] = useState('pending');

  // Update mockProjects with additional fields if needed
  const [enhancedProjects, setEnhancedProjects] = useState(() => 
    mockProjects.map(project => ({
      ...project,
      userName: "Ravi Shastri",
      userContact: "+91 6006925354",
      projectType: "Open",
      status: "pending" // Add default status
    }))
  );

  const updateProjectStatus = (id: string, newStatus: 'approved' | 'rejected') => {
    setEnhancedProjects(prevProjects =>
      prevProjects.map(project =>
        project.id === id ? { ...project, status: newStatus } : project
      )
    );
  };

  // Filter projects based on status
  const filteredProjects = enhancedProjects.filter(project => project.status === currentStatus);

  const totalRecords = filteredProjects.length;
  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalRecords);
  const currentProjects = filteredProjects.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (rows: number) => {
    setItemsPerPage(rows);
    setPage(1);
  };

  return (
    <div className="p-4 md:p-5 font-inter overflow-x-hidden overflow-y-auto h-[calc(90vh)] relative">
      <div className="md:flex md:justify-between md:items-center mb-8">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Projects</h1>
      </div>
      
      <div className="rounded-lg flex">
        {['pending', 'approved', 'rejected'].map(status => (
          <button
            key={status}
            onClick={() => setCurrentStatus(status)}
            className={`sm:px-8 lg:py-2 p-2 bg-white border-2 border-[#CECAF3] ${
              status === 'pending' ? 'border-r-0 rounded-l-md' : ''
            } ${
              status === 'rejected' ? 'border-l-0 rounded-r-md' : ''
            } ${
              currentStatus === status ? 'focus:bg-[#F1F1FF] text-[#7065F0]' : 'hover:bg-[#F1F1FF] hover:text-[#7065F0]'
            }`}
          >
            {status === 'pending' ? 'Pending Ads' : status === 'approved' ? 'Approved Ads' : 'Rejected Ads'}
          </button>
        ))}
      </div>

      <Suspense fallback={<LoadingProjectsSkeleton />}>
        <div className="bg-white p-4 lg:p-6 mt-6">
          <ProjectList projects={currentProjects} />
          <PaginationControls
            page={page}
            totalPages={totalPages}
            startIndex={startIndex}
            endIndex={endIndex}
            totalRecords={totalRecords}
            onPageChange={handlePageChange}
            rowsPerPage={itemsPerPage}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        </div>
      </Suspense>
    </div>
  );
}