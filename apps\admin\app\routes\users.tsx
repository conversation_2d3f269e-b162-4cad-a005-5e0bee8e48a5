import * as React from "react";
import { useState, useMemo } from 'react';
import PaginationControls from "../components/PaginationControls";
import { MoreVertical } from 'lucide-react';
import { mockProjects } from "../data/mockProjects";
import { Suspense } from "react";
import { Button } from "../components/ui/button";
import { useDebounce } from "../hooks/useDebounce";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu"; // Adjust path as needed
import { ChevronDown } from "lucide-react";

const MenuButton = ({ isOpen, onToggle }: { 
  isOpen: boolean; 
  onToggle: () => void;
}) => (
  <div className="relative">
    <button onClick={onToggle} className="p-2 hover:bg-gray-100">
      <MoreVertical size={20} />
    </button>
    {isOpen && (
      <div className="absolute right-0 mt-2 w-44 bg-white rounded-lg shadow-lg border border-gray-200 py-2 px-1 z-50
        animate-in fade-in slide-in-from-top-1 duration-200">
        <button className="w-full px-4 py-2 text-left text-red-500 hover:bg-neutral-100 rounded-md transition-colors"
          onClick={onToggle}>
          Delete
        </button>
      </div>
    )}
  </div>
);

const UserCard = ({
  profilePic,
  userName,
}: {
  profilePic: string;
  userName: string;
}) => {
  return (
    <div className="w-full py-3">
      <div className="flex flex-col md:flex-row gap-5 items-start md:items-center">
        <div className=" relative flex-shrink-0">
          <img 
            src={profilePic} 
            alt=""
            className="rounded-full object-cover w-14 h-14"
            loading="lazy"
          />
        </div>
        <div className="flex-grow">
          <div className="text-md font-medium">{userName}</div>
        </div>
      </div>
    </div>
  );
};

const ProjectList = ({ projects }: { projects: any[] }) => {
  const [isMenuOpen, setIsMenuOpen] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [selectedUserType, setSelectedUserType] = useState<string>('all');
  const debouncedSearch = useDebounce(searchInput, 300);

  // Filter projects based on debounced search
  const filteredProjects = useMemo(() => {
    return projects.filter((project) => {
      const matchesSearch = !debouncedSearch || 
        project.userName.toLowerCase().includes(debouncedSearch.toLowerCase());
      
      const matchesType = selectedUserType === 'all' || 
        project.userType === selectedUserType;

      return matchesSearch && matchesType;
    });
  }, [projects, debouncedSearch, selectedUserType]);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-x-4">
        <div className="relative w-72">
          <input
            type="text"
            placeholder="Search users"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
          {searchInput && (
            <button
              onClick={() => setSearchInput('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          )}
        </div>
        <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center gap-2 px-3 py-2 border rounded-md hover:bg-gray-50">
              <span>{selectedUserType === 'all' ? 'All' : selectedUserType}</span>
              <ChevronDown className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white font-inter">
              <DropdownMenuItem className="hover:cursor-pointer hover:bg-gray-50" onClick={() => setSelectedUserType('all')}>
                All
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:cursor-pointer hover:bg-gray-50" onClick={() => setSelectedUserType('Builder')}>
                Builder
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:cursor-pointer hover:bg-gray-50" onClick={() => setSelectedUserType('Owner')}>
                Owner
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Button className="hover:bg-orospaces-purple/10 hover:text-orospaces-purple" variant="outline">Export CSV</Button>
      </div>

      <div className="mb-4 grid grid-cols-1 md:grid-cols-7 gap-3 border-b border-gray-300 pb-2 overflow-x-hidden">
        <div className="font-medium text-gray-500 col-span-2">
          <span>Users</span>
        </div>
        <div className="hidden md:block truncate font-medium text-gray-500">
          User Type
        </div>
        <div className="hidden md:block truncate font-medium text-gray-500">
          User Contact
        </div>
        <div className="hidden md:block truncate font-medium text-gray-500">
          User Email
        </div>
        <div className="hidden md:block truncate font-medium text-gray-500">
          Last Subscribed
        </div>
        <div className="hidden md:flex font-medium text-gray-500 justify-between gap-x-5">
          <span className="truncate">Status</span>
          <span className="truncate">Actions</span>
        </div>
      </div>

      {filteredProjects.length > 0 ? (
        filteredProjects.map((project) => (
          <div
            key={project.id}
            className="grid grid-cols-1 md:grid-cols-7 gap-3 p-1 items-center border-b border-gray-200"
          >
            <div className="md:col-span-2">
              <UserCard
                profilePic={project.image}
                userName={project.userName}
              />
            </div>
            <div className="hidden md:block md:col-span-1">
              <div className="px-4 py-1 bg-[#FFF1C2] text-yellow-600 rounded-full text-xs inline-block">
                {project.userType}
              </div>
            </div>
            <div className="hidden md:block truncate md:col-span-1">
              {project.userContact}
            </div>
            <div className="hidden md:block truncate md:col-span-1">
              {project.userEmail}
            </div>
            <div className="hidden md:block truncate md:col-span-1">
              {project.postedDate}
            </div>
            <div className="hidden md:flex md:col-span-1 justify-between items-center gap-x-3">
              <div className="flex items-center justify-center truncate gap-2 rounded-sm py-1 px-2 bg-green-100 text-green-500">
                <div className="text-sm font-semibold truncate">Active</div>
              </div>
              <MenuButton
                isOpen={isMenuOpen === project.id}
                onToggle={() =>
                  setIsMenuOpen(isMenuOpen === project.id ? null : project.id)
                }
              />
            </div>
          </div>
        ))
      ) : (
        <p className="text-gray-500">No users found.</p>
      )}
    </div>
  );
};

function LoadingProjectsSkeleton() {
  return (
    <div className="animate-pulse space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-24 bg-gray-100 rounded-md border border-gray-200" />
      ))}
    </div>
  );
}

export default function Users() {
  const [page, setPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(8);

  const enhancedProjects = mockProjects.map(project => ({
    ...project,
    userName: "John Doe",
    userContact: "+91 6006925354",
  }));

  const totalRecords = enhancedProjects.length;
  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalRecords);
  const currentProjects = enhancedProjects.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (rows: number) => {
    setItemsPerPage(rows);
    setPage(1);
  };

  return (
    <div className="p-4 md:p-5 font-inter overflow-x-hidden overflow-y-auto h-[calc(90vh)] relative">
      <div className="md:flex md:justify-between md:items-center mb-8">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Manage Users</h1>
      </div>
      
      <Suspense fallback={<LoadingProjectsSkeleton />}>
        <div className="bg-white rounded-lg p-4 lg:p-6">
          <ProjectList projects={currentProjects} />
          <PaginationControls
            page={page}
            totalPages={totalPages}
            startIndex={startIndex}
            endIndex={endIndex}
            totalRecords={totalRecords}
            onPageChange={handlePageChange}
            rowsPerPage={itemsPerPage}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        </div>
      </Suspense>
    </div>
  );
}