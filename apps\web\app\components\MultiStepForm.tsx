import React, {HTMLProps, createContext, useCallback, useContext, useMemo, useState, useEffect, useRef} from 'react';
import {Slot, Slottable} from '@radix-ui/react-slot'
import {Path, UseFormReturn} from 'react-hook-form';
import {z} from 'zod';
import {clsx} from 'clsx';
import {twMerge} from 'tailwind-merge';

const cn = (...inputs: (string | undefined)[]) => {
  return twMerge(clsx(inputs));
};

interface MultiStepFormProps<T extends z.ZodType> {
  schema: T;
  form: UseFormReturn<z.infer<T>>;
  onSubmit: (data: z.infer<T>) => void;
  useStepTransition?: boolean;
  className?: string;
}

type StepProps = React.PropsWithChildren<{
  name: string;
  asChild?: boolean;
} & React.HTMLProps<HTMLDivElement>>;

const MultiStepFormContext = createContext<ReturnType<typeof useMultiStepForm> | null>(null);

export function MultiStepForm<T extends z.ZodType>({schema, form, onSubmit, children, className}: React.PropsWithChildren<MultiStepFormProps<T>>) {
  const steps = useMemo(() => React.Children.toArray(children).filter((child): child is React.ReactElement<StepProps> => React.isValidElement(child) && child.type === MultiStepFormStep), [children]);
  const header = useMemo(() => {
    return React.Children.toArray(children).find((child) => React.isValidElement(child) && child.type === MultiStepFormHeader);
  }, [children]);
  const footer = useMemo(() => {
    return React.Children.toArray(children).find((child) => React.isValidElement(child) && child.type === MultiStepFormFooter);
  }, [children]);

  const stepNames = steps.map((step) => step.props.name);
  const multiStepForm = useMultiStepForm(schema, form, stepNames);

  return (
    <MultiStepFormContext.Provider value={multiStepForm}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cn(className, 'flex size-full flex-col lg:flex-row relative')}>
        <div className="w-full lg:w-1/5 lg:z-50 left-0">{header}</div>
        <div className="w-full lg:w-[38vw] xl:w-[45vw] lg:translate-x-1/4 xl:translate-x-[15%] lg:ml-8 xl:ml-0">
          <div className="">
            {steps.map((step, index) => {
              const isActive = index === multiStepForm.currentStepIndex;
              return (
                <AnimatedStep key={step.props.name} direction={multiStepForm.direction} isActive={isActive} index={index} currentIndex={multiStepForm.currentStepIndex}>
                  {step}
                </AnimatedStep>
              );
            })}
          </div>
          {footer}
        </div>
        <div>
        <div className="lg:absolute lg:top-[-100px] lg:z-50 right-0 w-full lg:w-1/5 h-min hidden lg:flex flex-col p-6 bg-white border border-neutral-300 rounded-md">
          <h1 className='font-inter text-lg font-semibold mb-2'>Need any Support?</h1>
          <h3 className='font-outfit text-sm mb-4'>Call us at our toll-free number</h3>
          <button className='p-2 border border-neutral-300 rounded-md'>
            <img src="../Call_Image.svg" alt="call" />
          </button>
        </div>
        </div>
      </form>
    </MultiStepFormContext.Provider>
  );
}

export function MultiStepFormContextProvider(props: {children: (context: ReturnType<typeof useMultiStepForm>) => React.ReactNode}) {
  const ctx = useMultiStepFormContext();
  if (Array.isArray(props.children)) {
    const [child] = props.children;
    return (child as (context: ReturnType<typeof useMultiStepForm>) => React.ReactNode)(ctx);
  }
  return props.children(ctx);
}

export const MultiStepFormStep = React.forwardRef<HTMLDivElement, React.PropsWithChildren<{asChild?: boolean} & HTMLProps<HTMLDivElement>>>(
  function MultiStepFormStep({children, asChild, ...props}, ref) {
    const Cmp = asChild ? Slot : 'div';
    return (
      <Cmp ref={ref} {...props}>
        <Slottable>{children}</Slottable>
      </Cmp>
    );
  }
);

export function useMultiStepFormContext<Schema extends z.ZodType>() {
  const context = useContext(MultiStepFormContext) as ReturnType<typeof useMultiStepForm<Schema>>;
  if (!context) {
    throw new Error('useMultiStepFormContext must be used within a MultiStepForm');
  }
  return context;
}

export function useMultiStepForm<Schema extends z.ZodType>(schema: Schema, form: UseFormReturn<z.infer<Schema>>, stepNames: string[]) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [direction, setDirection] = useState<'forward' | 'backward'>();

  const isStepValid = useCallback(() => {
    const currentStepName = stepNames[currentStepIndex] as Path<z.TypeOf<Schema>>;
    if (schema instanceof z.ZodObject) {
      const currentStepSchema = schema.shape[currentStepName] as z.ZodType;
      if (!currentStepSchema) {
        return true;
      }
      const currentStepData = form.getValues(currentStepName) ?? {};
      const result = currentStepSchema.safeParse(currentStepData);
      return result.success;
    }
    throw new Error(`Unsupported schema type: ${schema.constructor.name}`);
  }, [schema, form, stepNames, currentStepIndex]);

  const nextStep = useCallback(<Ev extends React.SyntheticEvent>(e: Ev) => {
    e.preventDefault();
    const isValid = isStepValid();
    if (!isValid) {
      const currentStepName = stepNames[currentStepIndex] as Path<z.TypeOf<Schema>>;
      if (schema instanceof z.ZodObject) {
        const currentStepSchema = schema.shape[currentStepName] as z.ZodType;
        if (currentStepSchema) {
          const fields = Object.keys((currentStepSchema as z.ZodObject<never>).shape);
          const keys = fields.map((field) => `${currentStepName}.${field}`);
          for (const key of keys) {
            void form.trigger(key as Path<z.TypeOf<Schema>>);
          }
          return;
        }
      }
    }
    if (isValid && currentStepIndex < stepNames.length - 1) {
      setDirection('forward');
      setCurrentStepIndex((prev) => prev + 1);
    }
  }, [isStepValid, currentStepIndex, stepNames, schema, form]);

  const prevStep = useCallback(<Ev extends React.SyntheticEvent>(e: Ev) => {
    e.preventDefault();
    if (currentStepIndex > 0) {
      setDirection('backward');
      setCurrentStepIndex((prev) => prev - 1);
    }
  }, [currentStepIndex]);

  const goToStep = useCallback((index: number) => {
    if (index >= 0 && index < stepNames.length && isStepValid()) {
      setDirection(index > currentStepIndex ? 'forward' : 'backward');
      setCurrentStepIndex(index);
    }
  }, [isStepValid, stepNames.length, currentStepIndex]);

  const isValid = form.formState.isValid;
  const errors = form.formState.errors;

  return useMemo(() => ({
    form,
    currentStep: stepNames[currentStepIndex] as string,
    currentStepIndex,
    totalSteps: stepNames.length,
    isFirstStep: currentStepIndex === 0,
    isSecondLastStep: currentStepIndex === stepNames.length - 2,
    isLastStep: currentStepIndex === stepNames.length - 1,
    nextStep,
    prevStep,
    goToStep,
    direction,
    isStepValid,
    isValid,
    errors,
  }), [form, stepNames, currentStepIndex, nextStep, prevStep, goToStep, direction, isStepValid, isValid, errors]);
}

export const MultiStepFormHeader = React.forwardRef<HTMLDivElement, React.PropsWithChildren<{asChild?: boolean} & HTMLProps<HTMLDivElement>>>(
  function MultiStepFormHeader({children, asChild, ...props}, ref) {
    const Cmp = asChild ? Slot : 'div';
    return (
      <Cmp ref={ref} {...props}>
        <Slottable>{children}</Slottable>
      </Cmp>
    );
  }
);

export const MultiStepFormFooter = React.forwardRef<HTMLDivElement, React.PropsWithChildren<{asChild?: boolean} & HTMLProps<HTMLDivElement>>>(
  function MultiStepFormFooter({children, asChild, ...props}, ref) {
    const Cmp = asChild ? Slot : 'div';
    return (
      <Cmp ref={ref} {...props}>
        <Slottable>{children}</Slottable>
      </Cmp>
    );
  }
);

export function createStepSchema<T extends Record<string, z.ZodType>>(steps: T) {
  return z.object(steps);
}

interface AnimatedStepProps {
  direction: 'forward' | 'backward' | undefined;
  isActive: boolean;
  index: number;
  currentIndex: number;
}

function AnimatedStep({
  isActive,
  direction,
  children,
  index,
  currentIndex,
}: React.PropsWithChildren<AnimatedStepProps>) {
  const [shouldRender, setShouldRender] = useState(isActive);
  const stepRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isActive) {
      setShouldRender(true);
    } else {
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isActive]);

  useEffect(() => {
    if (isActive && stepRef.current) {
      const focusableElement = stepRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      );
      if (focusableElement) {
        (focusableElement as HTMLElement).focus();
      }
    }
  }, [isActive]);

  if (!shouldRender) {
    return null;
  }

  const baseClasses =
      'top-0 w-full h-full transition-all duration-300 ease-in-out animate-in fade-in zoom-in-95';
  
    const visibilityClasses = isActive ? 'opacity-100' : 'opacity-0 absolute';
  
    const transformClasses = isActive
      ? 'translate-x-0'
      : direction === 'backward' || index > currentIndex
      ? 'translate-x-full'
      : '-translate-x-full';
  
  
    const className = cn(baseClasses, visibilityClasses, transformClasses); 

    return (
      <div ref={stepRef} className={className} aria-hidden={!isActive}>
        {children}
      </div>
    );
}


