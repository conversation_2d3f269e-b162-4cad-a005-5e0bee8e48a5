import { useState } from 'react';
import { Search, X, ChevronDown } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import { Slider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Checkbox } from '../ui/checkbox';
import { Button } from '../ui/button';

interface FilterProps {
  onFilterChange: (filters: FilterState) => void;
  onReset: () => void;
}

export interface FilterState {
  location: string;
  landTypes: string[];
  projectTypes: string[];
  landArea: [number, number];
  budget: [number, number];
  isVerified: boolean;
}

const landTypeOptions = ['Agricultural', 'Non Agricultural', 'Open Land'];

const projectTypeOptions = ['Commercial', 'Redevelopment', 'SRA'];

export function FilterSection({ onFilterChange, onReset }: FilterProps) {
  const [filters, setFilters] = useState<FilterState>({
    location: '',
    landTypes: [],
    projectTypes: [],
    landArea: [0, 100000],
    budget: [0, 1000000],
    isVerified: false,
  });

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const handleReset = () => {
    setFilters({
      location: '',
      landTypes: [],
      projectTypes: [],
      landArea: [0, 100000],
      budget: [0, 1000000],
      isVerified: false,
    });
    onReset();
  };

  return (
    <div className="w-full sticky bg-white p-8 rounded-lg shadow">
      <div className="flex justify-between items-center mb-7">
        <div className="flex justify-center items-center gap-x-2">
          <img src="Filter.svg" className="w-6 h-6" />
          <h2 className="text-lg font-semibold">Filters</h2>
        </div>

        <button
          onClick={handleReset}
          className="text-sm text-[#0096FF] hover:text-[#0096FF]/80"
        >
          Reset All
        </button>
      </div>

      {/* Location Search */}
      <div className="mb-7">
        <div className="relative">
          <h1 className="font-semibold text-md mb-2">Location</h1>
          <Search className="absolute left-3 top-1/2 translate-y-1/2 h-4 w-4 text-[#0096FF]" />
          <Search className="absolute left-3 top-1/2 translate-y-1/2 h-4 w-4 text-[#0096FF]" />
          <input
            type="text"
            placeholder="e.g. Mumbai"
            value={filters.location}
            onChange={(e) => handleFilterChange({ location: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-[#7065F0]"
          />
          {filters.location && (
            <button
              onClick={() => handleFilterChange({ location: '' })}
              className="absolute right-3 top-1/2 translate-y-1/2"
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Land Types */}
      <Collapsible className="mb-7">
        <CollapsibleTrigger className="flex justify-between items-center w-full">
          <div className="font-medium">Type of Land</div>
          <Button variant="ghost" size="sm">
            <ChevronDown className="h-4 w-4" />
            <span className="sr-only">Toggle</span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-2">
          {landTypeOptions.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`land-${type}`}
                checked={filters.landTypes.includes(type)}
                onCheckedChange={(checked) => {
                  const newTypes = checked
                    ? [...filters.landTypes, type]
                    : filters.landTypes.filter((t) => t !== type);
                  handleFilterChange({ landTypes: newTypes });
                }}
              />
              <label htmlFor={`land-${type}`} className="text-[#515B6F]">
                {type}
              </label>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      {/* Project Types */}
      <Collapsible className="mb-7">
        <CollapsibleTrigger className="flex justify-between items-center w-full">
          <span className="font-medium">Type of Project</span>
          <Button variant="ghost" size="sm">
            <ChevronDown className="h-4 w-4" />
            <span className="sr-only">Toggle</span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-2">
          {projectTypeOptions.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`project-${type}`}
                checked={filters.projectTypes.includes(type)}
                onCheckedChange={(checked) => {
                  const newTypes = checked
                    ? [...filters.projectTypes, type]
                    : filters.projectTypes.filter((t) => t !== type);
                  handleFilterChange({ projectTypes: newTypes });
                }}
              />
              <label htmlFor={`project-${type}`} className="text-[#515B6F]">
                {type}
              </label>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      {/* Land Area Slider */}
      <div className="mb-10">
        <div className="font-medium mb-4">Land Area</div>
        <div className="">
          <Slider
            value={filters.landArea}
            min={0}
            max={100000}
            step={100}
            onValueChange={(value) =>
              handleFilterChange({ landArea: value as [number, number] })
            }
          />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span>{filters.landArea[0]} sqft</span>
            <span>{filters.landArea[1]} sqft</span>
          </div>
        </div>
      </div>

      {/* Budget Slider */}
      <div className="mb-10">
        <div className="font-medium mb-4">Development Budget</div>
        <div>
          <Slider
            value={filters.budget}
            min={0}
            max={1000000}
            step={100}
            onValueChange={(value) =>
              handleFilterChange({ budget: value as [number, number] })
            }
          />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span>₹ {filters.budget[0]} Lakh</span>
            <span>₹ {filters.budget[1]} Cr</span>
          </div>
        </div>
      </div>

      {/* Verified Properties Switch */}
      <div className="flex items-center justify-between">
        <label className="font-medium">Verified Properties</label>
        <Switch
          checked={filters.isVerified}
          onCheckedChange={(checked) =>
            handleFilterChange({ isVerified: checked })
          }
        />
      </div>
    </div>
  );
}
