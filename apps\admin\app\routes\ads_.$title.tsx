import { useParams } from "@remix-run/react";
import { mockProjects } from "../data/mockProjects";
import Navbar from "../components/Navbar";

export default function AdDetails() {
  const { title } = useParams();
  // In a real app, you'd fetch the project based on the title
  const project = mockProjects[0]; // For demo purposes

  return (
    <div className="min-h-screen bg-[#F9FAFB]">
      <div className="p-8">
        <div className="grid grid-cols-3 gap-8">
          {/* Main content area */}
          <div className="col-span-2 bg-white rounded-lg shadow-sm p-6">
            {/* Content will be added later */}
          </div>

          {/* User Details Sidebar */}
          <div className="col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-base font-semibold mb-4">User Details</h2>
              
              <div className="flex items-center gap-3 mb-6">
                <img 
                  src="/default-avatar.png" // Replace with actual avatar
                  alt={project.ownerName}
                  className="w-12 h-12 rounded-full"
                />
                <div>
                  <h3 className="font-medium">{project.ownerName}</h3>
                  <p className="text-sm text-gray-500">Owner Account</p>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-2">
                  <img src="/email-icon.svg" alt="Email" className="w-5 h-5" />
                  <span className="text-sm text-gray-600">{project.ownerEmail}</span>
                </div>
                <div className="flex items-center gap-2">
                  <img src="/phone-icon.svg" alt="Phone" className="w-5 h-5" />
                  <span className="text-sm text-gray-600">{project.ownerContact}</span>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">Last Subscribed</h3>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Status:</span>
                    <span className="text-green-600">{project.subscriptionStatus.status} ({project.subscriptionStatus.plan})</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Started on:</span>
                    <span>{project.subscriptionStatus.startDate}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Ending on:</span>
                    <span>{project.subscriptionStatus.endDate}</span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-3">Recent Ads</h3>
                <div className="space-y-3">
                  {project.recentAds.map((ad, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{ad.date}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{ad.status}</span>
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
