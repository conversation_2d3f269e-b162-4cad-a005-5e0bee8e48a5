{"name": "@orospace/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@remix-run/node": "^2.14.0", "@remix-run/react": "^2.14.0", "@remix-run/serve": "^2.14.0", "isbot": "^4.4.0", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@nx/cypress": "20.2.2", "@nx/eslint": "20.2.2", "@nx/eslint-plugin": "20.2.2", "@nx/js": "20.2.2", "@nx/react": "20.2.2", "@nx/remix": "20.2.2", "@nx/vite": "20.2.2", "@nx/web": "20.2.2", "@remix-run/dev": "^2.14.0", "@remix-run/testing": "^2.14.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/jest-dom": "6.4.2", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.3.1", "cypress": "^13.13.0", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jsdom": "~22.1.0", "nx": "20.2.2", "prettier": "^2.6.2", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0", "vite": "^5.0.0", "vitest": "^1.3.1"}}