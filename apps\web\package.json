{"private": true, "name": "web", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "type": "module", "dependencies": {"@hookform/resolvers": "^4.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-primitive": "^2.0.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/themes": "^3.2.0", "@remix-run/node": "^2.14.0", "@remix-run/react": "^2.14.0", "@remix-run/serve": "^2.14.0", "@tailwindcss/forms": "^0.5.10", "@vis.gl/react-google-maps": "^1.5.2", "autoprefixer": "^10.4.20", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.14.0", "input-otp": "^1.4.2", "isbot": "^4.4.0", "lucide-react": "^0.468.0", "motion": "^12.4.10", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "remix-auth": "^4.1.0", "remix-auth-form": "^3.0.0", "remix-auth-jwt": "^0.4.0", "remix-utils": "^8.1.0", "tailwind-merge": "^2.6.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "zod": "^3.24.2"}, "devDependencies": {"@remix-run/dev": "^2.14.0", "@tailwindcss/postcss": "^4.0.9", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.4.16"}, "engines": {"node": ">=20"}, "sideEffects": false, "nx": {"projectType": "application", "sourceRoot": "apps/web"}}