import { use<PERSON><PERSON><PERSON>, <PERSON>, useLoaderData } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/react";
import { useState, useEffect } from "react";
import { X, Info } from "lucide-react";
import { ClientOnly } from "remix-utils/client-only";
import {
  APIProvider,
  Map
} from '@vis.gl/react-google-maps';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  CarouselImage,
} from "../components/ui/carousel";
import { Button } from "../components/ui/button";
import { Card } from "../components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../components/ui/tooltip";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../components/ui/breadcrumb";

// Mock project data - in a real app, this would come from the backend
const mockProjects = [
  {
    id: '75458283',
    username: 'Orospaces User',
    phone: '+91 **********',
    email: '<EMAIL>',
    postedDate: '2024-09-12',
    price: '₹ 1.25 Cr - ₹ 1.5 Cr',
    area: '1365 sqft',
    title: 'Agricultural Land',
    location: '506 Division Road, Wadala Truck Terminal, Mumbai',
    project: 'Redevelopment',
    description: 'A unique opportunity to redevelop this historic property in the heart of the city. The site features multiple structures with potential for residential or mixed-use development.',
    images: [
      'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
    ],
    views: 120,
    inquiries: 9,
    status: 'all Enquiries',
    isVerified: true
  },
  // Other projects...
];

// Add this near the top where other interfaces/types are defined
type SubscriptionType = 'free' | 'premium';

// Project Summary Card Component
const ProjectSummaryCard = ({
  price,
  area,
  title,
  location,
  project,
  isVerified,
}: {
  price: string;
  area: string;
  title: string;
  location: string;
  project: string;
  isVerified: boolean;
}) => {
  return (
    <div className="w-full bg-transparent overflow-hidden mb-6">
      <div className="flex flex-col">
      <div className="flex justify-between items-center mb-2 gap-x-5 overflow-x-auto md:overflow-x-visible scrollbar-hide">
            <div>
              <div className="flex items-start gap-x-2">
              <h3 className="text-lg font-poppins font-semibold truncate mb-2">{title}</h3>
              {isVerified && (
                <img 
                  src="/Verified.svg"
                  alt="Verified"
                  className="size-[40%]"
                />
              )}
              </div>
              <p className="text-sm text-gray-500 truncate mb-2">{location}</p>
            </div>
            <div className="flex gap-2">
              <button 
                className="p-2 rounded-full bg-[#F1F1FF] hover:bg-[#E5E5FF] transition-colors"
              >
                <img src="/mdi-light_share.svg" />
              </button>
              <button 
                className="p-2 rounded-full bg-[#F1F1FF] hover:bg-[#E5E5FF] transition-colors"
              >
                <img src="/bookmark-icon.svg" />
              </button>
            </div>
          </div>
        

        <div className="w-full flex flex-col sm:flex-row justify-start gap-y-4 sm:gap-y-0 mb-4">
          <div className="flex gap-x-6 xl:gap-x-12 text-nowrap overflow-x-auto scrollbar-hide">
            <div className="pr-8 xl:pr-12 border-r-2">
              <h3 className="text-sm text-gray-500">Budget Estimate</h3>
              <h1 className="text-medium font-medium mt-1">{price}</h1>
            </div>
            <div className="pr-8 xl:pr-12 border-r-2">
              <h3 className="text-sm text-gray-500">Land Area</h3>
              <h1 className="text-medium font-medium mt-1">{area}</h1>
            </div>
            <div>
              <h3 className="text-sm text-gray-500">Project type</h3>
              <div className="flex items-center gap-x-2">
                <h1 className="text-medium font-medium mt-1">{project}</h1>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-gray-400 mt-1" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Type of development project</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
        <p className="text-sm text-neutral-400">Posted by : <span className="font-medium text-neutral-600">Owner</span></p>
      </div>
    </div>
  );
};

// Loader function to fetch project data
export async function loader({ params }: LoaderFunctionArgs) {
  const { title } = params;
  
  // In a real app, you would fetch data from your API
  // For now, we'll find the project in our mock data by converting the URL-friendly title back
  const normalizedTitle = title?.replace(/-/g, ' ');
  
  // Find the project with a case-insensitive match
  const project = mockProjects.find(p => 
    p.title.toLowerCase() === normalizedTitle?.toLowerCase()
  );
  
  if (!project) {
    throw new Response("Project not found", { status: 404 });
  }
  
  return json({ project });
}

// Image Gallery Component
const ImageGallery = ({ images }: { images: string[] }) => {
  const [showCarousel, setShowCarousel] = useState(false);
  const [startIndex, setStartIndex] = useState(0);
  
  const openCarousel = (index: number) => {
    setStartIndex(index);
    setShowCarousel(true);
    document.body.style.overflow = 'hidden';
  };
  
  const closeCarousel = () => {
    setShowCarousel(false);
    document.body.style.overflow = '';
  };
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showCarousel) {
        closeCarousel();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showCarousel]);
  
  const remainingImages = images.length > 3 ? images.length - 3 : 0;
  
  return (
    <>
      {/* Desktop Grid Layout */}
      <div className="hidden w-full h-[400px] relative md:flex gap-4 overflow-hidden">
        {/* Main large image */}
        <div 
          className="w-2/3 h-full cursor-pointer overflow-hidden"
          onClick={() => openCarousel(0)}
        >
          <img 
            src={images[0]} 
            alt={`Project image 1`} 
            className="w-full h-full object-cover rounded-lg"
          />
        </div>
        
        <div className="w-1/3 h-full relative grid grid-rows-2 grid-flow-row gap-4">
        {images.length > 1 && (
          <div 
            className="w-full h-full cursor-pointer overflow-hidden"
            onClick={() => openCarousel(1)}
          >
            <img 
              src={images[1]} 
              alt={`Project image 2`} 
              className="object-contain rounded-lg"
            />
          </div>
        )}
        
        {/* Third image with overlay if there are more */}
        {images.length > 2 && (
          <div 
            className="w-full h-full cursor-pointer overflow-hidden"
            onClick={() => openCarousel(2)}
          >
            <img 
              src={images[2]} 
              alt={`Project image 3`} 
              className=" object-cover rounded-lg"
            />
            {remainingImages > 0 && (
              <div className="absolute inset-0 bg-white/60 bg-opacity-60 flex items-center justify-center">
                <span className="text-gray-700 text-3xl font-semibold">+{remainingImages}</span>
              </div>
            )}
          </div>
        )}
        </div>
      </div>
      
      {/* Mobile Carousel */}
      <div className="md:hidden w-full h-[300px] rounded-lg overflow-hidden">
        <Carousel className="w-full h-full">
          <CarouselContent>
            {images.map((image, index) => (
              <CarouselItem key={index}>
                <div 
                  className="h-[300px] w-full relative"
                  onClick={() => openCarousel(index)}
                >
                  <CarouselImage 
                    src={image} 
                    alt={`Project image ${index + 1}`}
                    className="h-full"
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
      
      {/* Full-screen Carousel Overlay */}
      {showCarousel && (
        <div className="fixed inset-0 z-50 bg-white/50 backdrop-blur-sm flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 z-50 text-black hover:bg-white/20"
            onClick={closeCarousel}
          >
            <X className="h-8 w-8" />
          </Button>
          
          <div className="w-full h-full max-h-[80vh] px-4">
            <Carousel className="w-full h-full" opts={{ startIndex }}>
              <CarouselContent>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="flex h-full items-center justify-center p-2">
                      <img 
                        src={image} 
                        alt={`Project image ${index + 1}`}
                        className="max-h-[80vh] max-w-full object-contain"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-4" />
              <CarouselNext className="right-4" />
            </Carousel>
          </div>
        </div>
      )}
    </>
  );
};

const MapComponent = () => {
  return (
    <APIProvider apiKey="AIzaSyCgZjXgPNG0SDagPLz7MMfoWFrueJ5d6Sk">
      <Map
        defaultZoom={15}
        defaultCenter={{ lat: 19.0760, lng: 72.8777 }}
        gestureHandling={'greedy'}
        disableDefaultUI={true}
        className="w-full h-full"
      />
    </APIProvider>
  );
};

// Modify the ProjectDetail component to include subscription status
export default function ProjectDetail() {
  const { project } = useLoaderData<typeof loader>();
  // For demo purposes, setting it to 'free'. In real app, this would come from auth/user context
  const subscriptionType: SubscriptionType = 'free';
  
  return (
    <div className="flex flex-col min-h-screen">
      <div className="mx-auto p-8 w-full">
        {/* Add Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/projects">Projects</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{project.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        
        {/* Image Gallery */}
        <div className=" mb-8">
          <ImageGallery images={project.images} />
        </div>
        
        {/* Project Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
              
              {/* Project Summary Card */}
              <ProjectSummaryCard 
                price={project.price}
                area={project.area}
                title={project.title}
                location={project.location}
                project={project.project}
                isVerified={project.isVerified}
              />

              {/* Owner Details Section */}
              <h3 className="text-lg font-poppins text-orospaces-purple font-medium py-6">Owner Details</h3>
              <div className="relative">
                <div className={`grid grid-cols-2 gap-7 ${subscriptionType === 'free' ? 'blur-sm' : ''}`}>
                  <div>
                    <h3 className="text-md font-poppins font-medium mb-2">Name</h3>
                    <p className="text-sm text-gray-500">{'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="text-md font-poppins font-medium mb-2">Email</h3>
                    <p className="text-sm text-gray-500">{'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="text-md font-poppins font-medium mb-2">Phone</h3>
                    <p className="text-sm text-gray-500">{'N/A'}</p>
                  </div>
                </div>
                
                {subscriptionType === 'free' && (
                  <div className="absolute inset-0 flex flex-col gap-y-3 items-center justify-center">
                    <img src="/padlock (1).svg" className="size-10" />
                    <Button 
                      className="bg-orospaces-purple hover:bg-orospaces-purple/90 rounded-full px-5 py-2 font-inter text-white"
                      
                    >
                      Get Owner Details
                    </Button>
                  </div>
                )}
              </div>
              
              {/* Map Section */}
              <h3 className="text-lg font-poppins text-orospaces-purple font-medium mt-8 mb-7">Locate Land</h3>
              <h3 className="text-md font-poppins font-medium mb-4">Explore Neighbourhood</h3>
              <div className="w-full h-[400px] rounded-lg overflow-hidden">
                <ClientOnly fallback={
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    Loading map...
                  </div>
                }>
                  {() => <MapComponent />}
                </ClientOnly>
              </div>
              
              
            </div>
          </div>
          
          <div className="md:col-span-1">
            <img src="/ad2.svg" className="object-contain" />
          </div>p
        </div>
      </div>
    </div>
  );
}
