import { Form, useActionData, useNavigation } from "@remix-run/react";
import type { ActionFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { signup } from "../services/auth.server";
import { useState } from "react";
import { cn } from "../lib/utils";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Link } from "@remix-run/react";
import { Loader2, Eye, EyeOff } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselDots,
  CarouselImage,
} from "../components/ui/carousel";
import { Checkbox } from "../components/ui/checkbox";

type ActionData = { error: string } | undefined;

// Loader to check if user is already authenticated
// export async function loader({ request }: LoaderFunctionArgs) {
//   // Check if user is already authenticated by querying the backend
  
//   return {};
// }

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const name = formData.get("name") as string;
    let phone = formData.get("phone") as string;
    const userType = formData.get("userType") as "owner" | "builder";
    
    let digits = phone.replace(/^\+/, '').replace(/\D/g, '');
    
    digits = '91' + digits;
    
    phone = '+' + digits;
    
    const phoneRegex = /^\+91\d{10}$/;
    if (!phoneRegex.test(phone) && digits.length !== 10) {
      throw new Error("Phone number invalid");
    }

    const passRegex = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,32}$/;
    if (!passRegex.test(password)) {
      throw new Error("Password invalid");
    }

    
    // Make API call to signup endpoint
    await signup({
      name,
      email,
      password,
      phone,
      userType
    });
    
    const userData = {
      name,
      email,
      phone,
      userType
    };
    
    const encodedPhone = encodeURIComponent(Buffer.from(phone).toString('base64'));
    
    return redirect(`/verification?p=${encodedPhone}`);
  } catch (error) {
    return { 
      error: error instanceof Error ? error.message : "Registration failed. Please try again." 
    };
  }
}

export default function SignupPage({ className, ...props }: React.ComponentProps<"div">) {
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isLoading = navigation.state === "submitting";
  const [showPassword, setShowPassword] = useState(false);
  const [userType, setUserType] = useState("owner");
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const images = ["/Image_1.svg", "/Image_2.svg", "/Image_3.svg"];

  return (
    <div className={cn("w-full h-screen flex justify-between items-center gap-6 p-8 font-poppins", className)} {...props}>
      <div className="hidden md:flex w-1/2 h-full items-center">
        <Carousel className="w-full max-h-[95vh]">
          <CarouselContent>
            {images.map((image, index) => (
              <CarouselItem key={index}>
                <CarouselImage src={image} alt="" className="max-h-[95vh] object-contain" />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselDots 
            count={3} 
            className="bottom-6 left-1/2 -translate-x-1/2 top-auto"
          />
        </Carousel>
      </div>
      
      <div className="w-full md:w-1/2 h-full py-12 flex items-center justify-center">
        <Form method="post" className="w-full max-w-md space-y-6">
          <div className="flex flex-col items-center text-center mt-6">
            <h1 className="text-2xl font-semibold mb-1">Create your account with us!</h1>
          </div>

          {actionData?.error && (
            <div className="p-1 text-sm text-red-500 bg-red-50 rounded-md">
              {actionData.error}
            </div>
          )}
          
          <div className="mb-6">
            <p className="text-[#9794AA] text-sm mb-3">You're creating an account as?</p>
            
            <div className="grid grid-cols-2 gap-4">
              <div 
                className={cn(
                  "flex items-center justify-center rounded-lg border-2 p-3 cursor-pointer",
                  userType === "owner" 
                    ? "border-orospaces-purple bg-orospaces-purple/10" 
                    : "border-[#E2E8F0]"
                )}
                onClick={() => setUserType("owner")}
              >
                <div className={cn(
                  "relative flex items-center justify-center h-3 w-3 rounded-full ring-offset-2",
                  userType === "owner" ? "bg-orospaces-purple ring-orospaces-purple ring-2" : "border-2 border-primary"
                )}>
                </div>
                <span className="ml-3">Land Owner</span>
              </div>
              
              <div 
                className={cn(
                  "flex items-center justify-center rounded-lg border-2 p-3 cursor-pointer",
                  userType === "builder" 
                    ? "border-orospaces-purple bg-orospaces-purple/10" 
                    : "border-[#E2E8F0]"
                )}
                onClick={() => setUserType("builder")}
              >
                <div className={cn(
                  "relative flex items-center justify-center h-3 w-3 rounded-full ring-offset-2",
                  userType === "builder" ? "bg-orospaces-purple ring-orospaces-purple ring-2" : "border-2 border-primary"
                )}>
                </div>
                <span className="ml-3">Builder</span>
              </div>
            </div>
            
            <input 
              type="hidden" 
              name="userType" 
              value={userType} 
            />
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-[#9794AA] text-sm">Full Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                placeholder="Enter your full name"
                required
                className="p-3 h-12 rounded-md"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-[#9794AA] text-sm">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email address"
                required
                className="p-3 h-12 rounded-md"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-[#9794AA] text-sm">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Create your password"
                  required
                  className="p-3 h-12 rounded-md pr-10"
                />
                <button 
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-[#9794AA] text-sm">Mobile Number</Label>
              <div className="flex">
                <div className="flex items-center justify-between gap-x-1 px-3 border border-r-0 rounded-l-md bg-gray-50 text-gray-500">
                  <p>IND</p>
                  <p>+91</p>
                </div>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder="Enter your mobile number"
                  required
                  className="p-3 h-12 rounded-none rounded-r-md"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 mt-4">
              <Checkbox 
                id="terms" 
                checked={agreeToTerms}
                onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                className=""
              />
              <div className="text-sm">
                <label
                  htmlFor="terms"
                  className="text-[#9794AA]"
                >
                  I agree to Orospaces{" "}
                  <Link to="/terms" className="text-primary hover:underline">T&C</Link>,{" "}
                  <Link to="/privacy" className="text-primary hover:underline">Privacy Policy</Link>{" "}
                  and{" "}
                  <Link to="/cookies" className="text-primary hover:underline">Cookie Policy</Link>.
                </label>
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full h-10 bg-orospaces-purple hover:bg-orospaces-purple/90 text-white"
            disabled={isLoading || !agreeToTerms}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              </>
            ) : (
              "Verify and Create an Account"
            )}
          </Button>

          <div className="text-center text-sm text-[#9794AA]">
            Already have an account?{" "}
            <Link
              to="/login"
              className="text-primary hover:underline"
            >
              Login
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
}