import { useState } from "react";
import { Card, CardContent, CardFooter } from "../components/ui/card"; 

interface SubscriptionStats {
  adsPosted: number;
  adsInquired: number;
  maxAds: number;
  currentPlan: string;
  purchaseDate: string;
  expiryDate: string;
}

export default function Subscription() {
  // In a real app, this would come from your backend/API
  const [stats] = useState<SubscriptionStats>({
    adsPosted: 7,
    adsInquired: 7,
    maxAds: 10,
    currentPlan: "Free Trial",
    purchaseDate: "1 Aug 2024",
    expiryDate: "15th Sep 2024"
  });

  return (
    <div className="min-h-screen bg-[#F9FAFB] p-8 font-inter">
      
      <h1 className="text-2xl font-bold mb-6">My Subscription</h1>
      
      <h2 className="text-md mb-6 font-semibold text-neutral-700">Current Plan</h2>

      <Card className="w-full shadow-lg">
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-8">
            <div>
              <h3 className="font-semibold text-lg mb-2">{stats.currentPlan}</h3>
              <p className="text-sm text-gray-500">Purchased on: {stats.purchaseDate}</p>
            </div>
            <p className="text-sm text-red-500">Expires on: {stats.expiryDate}</p>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div>
              <div className="flex justify-between text-sm font-medium mb-3">
                <span>Ads Posted</span>
                <span className="text-neutral-700">{stats.adsPosted} out of {stats.maxAds}</span>
              </div>
              <div className="h-2 bg-gray-100 rounded-full">
                <div 
                  className="h-full bg-[#7C3AED] rounded-full"
                  style={{ width: `${(stats.adsPosted / stats.maxAds) * 100}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm font-medium mb-3">
                <span>Ads Inquired</span>
                <span className="text-neutral-700">{stats.adsInquired} out of {stats.maxAds}</span>
              </div>
              <div className="h-2 bg-gray-100 rounded-full">
                <div 
                  className="h-full bg-[#FFC107] rounded-full"
                  style={{ width: `${(stats.adsInquired / stats.maxAds) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-end gap-4 p-6">
          <button 
            className="px-4 py-2 text-sm text-neutral-600 border border-neutral-600 rounded-lg hover:bg-gray-50"
          >
            Cancel Subscription
          </button>
          <button 
            className="px-4 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700"
          >
            Upgrade Plan
          </button>
        </CardFooter>
      </Card>

      <h3 className="text-neutral-700 text-md font-medium pt-12 pb-16">Subscription Plans</h3>
      <div className="space-y-8 lg:grid lg:grid-cols-3 sm:gap-6 xl:gap-10 lg:space-y-0">
          
          <div className="flex flex-col items-start p-8 mx-auto max-w-lg text-gray-900 bg-white rounded-lg border border-gray-100 shadow dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white">
              <h3 className="mb-3 text-2xl font-semibold">Basic Plan</h3>
              <div className="flex justify-center items-center mb-3 gap-x-2">
                  <span className="mr-2 text-5xl font-extrabold">$0</span>
                  <span className="text-gray-500 dark:text-gray-400">per editor/month billed monthly</span>
              </div>
              
              <ul role="list" className="my-8 space-y-4 text-left">
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>30h Fast generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Unlimited Relaxed generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>General commercial terms</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" alt="check" />
                      <span>Access to member gallery</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" alt="check" />
                      <span>Optional credit top ups</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" alt="check" />
                      <span>3 concurrent fast jobs</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" alt="check" />
                      <span>12 concurrent fast jobs</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Access to member gallery</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Optional credit top ups</span>
                  </li>
              </ul>
              <a href="#" className="w-full text-orospaces-purple/50 bg-[#F1F1FF] font-semibold rounded-lg text-md px-5 py-3 text-center">Choose Plan</a>
          </div>
          
          <div className="flex flex-col items-start justify-between p-8 mx-auto max-w-lg text-gray-900 bg-white rounded-lg border border-gray-100 shadow dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white">
              <div>
              <h3 className="mb-3 text-2xl font-semibold">Standard Plan</h3>
              <div className="flex justify-center items-center my-3 gap-x-2">
                  <span className="mr-2 text-5xl font-extrabold">$30</span>
                  <span className="text-gray-500 dark:text-gray-400">per editor/month billed monthly</span>
              </div>
              
              <ul role="list" className="my-8 space-y-4 text-left">
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>15h Fast generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Unlimited generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>General commercial terms</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Access to member gallery</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Optional credit top ups</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>3 concurrent fast jobs</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Optional credit top ups</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Optional credit top ups</span>
                  </li>
              </ul>
              </div>
              <a href="#" className="w-full text-orospaces-purple bg-[#F1F1FF] font-semibold rounded-lg text-md px-5 py-3 text-center">Choose Plan</a>
          </div>

          <div className="flex flex-col items-start justify-between p-8 mx-auto max-w-lg text-gray-900 bg-white rounded-lg border border-gray-100 shadow dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white">
            <div>
              <h3 className="mb-3 text-2xl font-semibold">Pro Plan</h3>
              <div className="flex justify-center items-center my-3 gap-x-2">
                  <span className="mr-2 text-5xl font-extrabold">$60</span>
                  <span className="text-gray-500 dark:text-gray-400">per editor/month billed monthly</span>
              </div>
              
              <ul role="list" className="my-8 space-y-4 text-left">
              <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>30h Fast generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Unlimited Relaxed generations</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>General commercial terms</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Access to member gallery</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>Optional credit top ups</span>
                  </li>
                  <li className="flex items-center space-x-3">
                      <img className="size-6" src="/check.svg" />
                      <span>3 concurrent fast jobs</span>
                  </li>
              </ul>
              </div>
              <a href="#" className="w-full text-orospaces-purple bg-[#F1F1FF] font-semibold rounded-lg text-md px-5 py-3 text-center">Choose Plan</a>
          </div>
      </div>
    </div>
  );
}
