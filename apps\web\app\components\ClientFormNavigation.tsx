// app/components/client-form-navigation.tsx
import { ClientOnly } from "remix-utils/client-only";
import { useMultiStepFormContext } from "./MultiStepForm";

function FormNavigationContent() {
  const {
    isFirstStep,
    isSecondLastStep,
    isLastStep,
    nextStep,
    prevStep,
    currentStepIndex,
    totalSteps,
    currentStep
  } = useMultiStepFormContext();

  if (currentStep === 'Documents & Others') {
    return null;
  }

  return (
    <div className="flex justify-center items-center w-full">
      <div className="flex gap-3 mt-20">
        {!isFirstStep && (
          <button
            type="button"
            onClick={prevStep}
            className="px-14 py-2 border rounded-full font-inter hover:bg-gray-50"
          >
            Previous
          </button>
        )}
        {isSecondLastStep && (
          <button
            type="button"
            onClick={nextStep}
            className="px-4 py-2 bg-orospaces-purple font-inter text-white rounded-full"
          >
            Review and Post Ad
          </button>
        )}
        {!isLastStep && !isSecondLastStep ? (
          <button
            type="button"
            onClick={nextStep}
            className="px-14 py-2 bg-orospaces-purple font-inter text-white rounded-full"
          >
            Next
          </button>
        ) : (
          <button
            type="submit"
            className="px-14 py-2 bg-orospaces-bg-orospaces-purple font-inter text-white rounded-full"
          >
            Post Project
          </button>
        )}
      </div>
    </div>
  );
}

export default function FormNavigation() {
  return (
    <ClientOnly fallback={<div>Loading...</div>}>
      {() => <FormNavigationContent />}
    </ClientOnly>
  );
}